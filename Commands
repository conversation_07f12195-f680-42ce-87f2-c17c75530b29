//Install playwright
npm install -D @playwright/test@latest
npx playwright install

//Run playwright in debug
npx playwright test --debug

npx playwright test --ui

npx playwright test --headed

//Open last report
npx playwright show-report

//Run the test with the title
npx playwright test -g "Build automation org"

npx playwright test --project=e2e --headed    

npx playwright test ./Web App Regression/ --headed

//Run neo Build Org setup
npx playwright test --project=setup-neo --headed

//Run all neo projects, in parallel with no setup, headless (quickest execution)
npx playwright test --project=e2e-neo* --workers=4 --no-deps
//Same above but run only the failed tests from the last run
npx playwright test --project=e2e-neo* --workers=4 --no-deps --last-failed

//Use this below to run FULL neo pack
npm run neo
//runs build neo org headed, then parallel test headless, then serial test headless


//Set ENV variables below with JIRA Email and API key
$env:JIRA_API_KEY=""
$env:JIRA_EMAIL=""

//Will run the FULL Webapp regression testpack and upload the video evidence to <PERSON><PERSON> if no issues
//REMOVE EXISTING EVIDENCE BEFORE RUNNING THIS
npm run webappRegression DEV-43474

//upload current video evidence to Jira, <folder name> <Parent Issue>
node ./test-data/updateJiraWithEvidence.js Webapp DEV-43474

//upload current Webapp video evidence to Jira
//Ensure only evidence you want uploaded is in the webapp folder
npm run webappUpload DEV-43474

//Run E2E tests for all neo project viewports and upload evidence to Jira in one command
//Example:
// node ./test-data/updateJiraWithEvidence.js neo DEV-49711
//This will:
//1. Process all neo viewport tests (e2e-neo, e2e-neo-small, e2e-neo-medium)
//2. Look for matching video evidence in Videos/e2e-neo*, Videos/e2e-neo-small*, Videos/e2e-neo-medium*
//3. Upload evidence and update all matching subtasks in the Jira ticket
//4. Set subtasks to Done after successful upload

//NOTE: Make sure to run npm run neo first to generate the video evidence before uploading

//Run FULL Neo regression pack and upload evidence to Jira in one command
//Make sure to remove any existing evidence first from Videos/e2e-neo* folders
npm run neoRegression DEV-49711
//This will:
//1. Run npm run neo (build org + parallel + serial tests)
//2. If tests pass, automatically upload evidence using neo command
//3. Process all viewport sizes (e2e-neo, e2e-neo-small, e2e-neo-medium)
//4. Upload evidence and update all matching subtasks
//5. Set subtasks to Done

//Or run just the evidence upload if tests already completed:
npm run neoUpload DEV-49711

