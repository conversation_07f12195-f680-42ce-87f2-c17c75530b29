const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

const jiraBaseUrl = 'https://matrixbooking.atlassian.net/';

/*
//This script processes subtasks for a given Jira parent ticket, attaches matching video files from a specified project folder,
//adds a comment indicating that the test passed and evidence is attached, and transitions each subtask to "Done".
USAGE:
//Set JIRA ENV variables in .env file
// Template in test_data folder
// https://id.atlassian.com/manage-profile/security/api-tokens

//upload current video evidence to <PERSON><PERSON>, <folder name> <Parent Issue>
node ./test-data/updateJiraWithEvidence.js Webapp DEV-43474

//upload current Webapp video evidence to Jira
npm run webappUpload DEV-43474
 */

// Check if .env file exists in the root directory
const envFileExists = fs.existsSync('./.env');
// Log whether .env file exists
console.log(`ENV file exists in root: ${envFileExists ? 'Yes' : 'No'}`);

// Load environment variables from .env file
require('dotenv').config();

// Log credential status
console.log(`JIRA_EMAIL: ${process.env.JIRA_EMAIL ? 'Present' : 'Missing'}`);
console.log(`JIRA_API_KEY: ${process.env.JIRA_API_KEY ? 'Present' : 'Missing'}`);

const auth = {
  username: process.env.JIRA_EMAIL || '',
  password: process.env.JIRA_API_KEY || '',
};

/**
 * Fetch subtasks of a parent ticket.
 */
const getSubtasks = async (parentTicketId) => {
  const apiUrl = `${jiraBaseUrl}/rest/api/2/issue/${parentTicketId}`;
  try {
    const response = await axios.get(apiUrl, { auth });
    return response.data.fields.subtasks || [];
  } catch (error) {
    console.error(`Error fetching subtasks for ${parentTicketId}:`, error.response?.data || error.message);
    return [];
  }
};

/**
 * Attach a file to a Jira issue (subtask).
 */
const attachFileToJira = async (ticketId, filePath) => {
  const apiUrl = `${jiraBaseUrl}/rest/api/2/issue/${ticketId}/attachments`;
  const formData = new FormData();
  formData.append('file', fs.createReadStream(filePath));

  try {
    await axios.post(apiUrl, formData, {
      headers: {
        ...formData.getHeaders(),
        'X-Atlassian-Token': 'no-check',
      },
      auth,
    });
    console.log(`File attached successfully to ${ticketId}: ${path.basename(filePath)}`);
  } catch (error) {
    console.error(`Error attaching file to ${ticketId}:`, error.response?.data || error.message);
  }
};

/**
 * Add a comment to a Jira issue (subtask).
 */
const addCommentToJira = async (ticketId, comment) => {
  const apiUrl = `${jiraBaseUrl}/rest/api/2/issue/${ticketId}/comment`;
  const data = { body: comment };

  try {
    await axios.post(apiUrl, data, { auth });
    console.log(`Comment added to ${ticketId}: ${comment}`);
  } catch (error) {
    console.error(`Error adding comment to ${ticketId}:`, error.response?.data || error.message);
  }
};

/**
 * Transition a Jira issue (subtask) to "Done".
 */
const transitionIssueToDone = async (ticketId) => {
  const apiUrl = `${jiraBaseUrl}/rest/api/2/issue/${ticketId}/transitions`;

  const transitionData = {
    transition: {
      id: '31', // Replace with the actual transition ID for "Done"
    },
  };

  try {
    await axios.post(apiUrl, transitionData, { auth });
    console.log(`Subtask ${ticketId} marked as Done.`);
  } catch (error) {
    console.error(`Error transitioning ${ticketId} to Done:`, error.response?.data || error.message);
  }
};

/**
 * Recursively find all video files and folders in the directory and its subdirectories.
 */
const getVideoFiles = (folderPath) => {
  let videoFolders = [];
  const items = fs.readdirSync(folderPath);

  items.forEach((item) => {
    const fullPath = path.join(folderPath, item);
    if (fs.statSync(fullPath).isDirectory()) {
      videoFolders.push(fullPath);
      videoFolders = videoFolders.concat(getVideoFiles(fullPath));
    } else if (path.extname(item).toLowerCase() === '.webm') {
      videoFolders.push(fullPath);
    }
  });

  return videoFolders;
};

/**
 * Add helper to determine org data file
 */
const getOrgData = (projectName) => {
    if (projectName.toLowerCase().includes('neo')) {
        return require('../test-data/neo/Seed.json');
    }
    return require('../test-data/e2e/Org0_data.json');
};

/**
 * Main function to match videos and attach to subtasks
 */
const processSubtasks = async (parentTicketId, VideoFolder) => {
  const subtasks = await getSubtasks(parentTicketId);
  const org = getOrgData(path.basename(VideoFolder)); // Get org data based on project

  if (subtasks.length === 0) {
    console.log(`No subtasks found for ${parentTicketId}.`);
    return;
  }

  console.log(`Found ${subtasks.length} subtasks for ${parentTicketId}.`);

  const videoFolders = getVideoFiles(VideoFolder);

  for (const subtask of subtasks) {
    const subtaskKey = subtask.key;
    const subtaskSummary = subtask.fields.summary;

    console.log(`Processing subtask: ${subtaskKey} - ${subtaskSummary}`);

    const matchingFolders = videoFolders.filter((folder) => {
      const folderName = path.basename(folder).toLowerCase();
      const subtaskSummaryLower = subtaskSummary.toLowerCase();

      return subtaskSummaryLower.startsWith(folderName)
      // || subtaskSummaryLower.includes(folderName);
    });

    if (matchingFolders.length === 0) {
      console.log(`No matching video folder found for ${subtaskKey} (${subtaskSummary}).`);
      continue;
    }
    //looks for video files in a specified folder and matches them to the subtasks based on folder names (ignoring dates).
    for (const folder of matchingFolders) {
      const videoFilesInFolder = fs.readdirSync(folder).filter((file) => path.extname(file).toLowerCase() === '.webm');
      for (const videoFile of videoFilesInFolder) {
        const videoFilePath = path.join(folder, videoFile);
        await attachFileToJira(subtaskKey, videoFilePath);
        const comment = `Test passed ${org.orgENV} ${org.orgName} \nevidence attached - File: ${videoFile}`;
        addCommentToJira(subtaskKey, comment);
        transitionIssueToDone(subtaskKey);
      }
    }
  }
};

// Add constant for neo project folders
const NEO_PROJECTS = ['e2e-neo', 'e2e-neo-small', 'e2e-neo-medium'];

// Modify the main section at the bottom of the file
const parentTicketId = process.argv[3];
const projectName = process.argv[2];

// Validate the parent ticket ID and project name
if (!parentTicketId || !parentTicketId.startsWith('DEV')) {
    console.error('Error: Invalid or missing parent ticket ID. Usage: <ticket-id> (e.g., DEV-1234)');
    process.exit(1);
}

if (!projectName) {
    console.error('Error: Missing project name. Usage: <project-name> <ticket-id>');
    process.exit(1);
}

console.log(`Processing subtasks for parent ticket: ${parentTicketId}`);

if (projectName.toLowerCase() === 'neo') {
    // Handle all neo projects
    console.log('Processing all Neo projects:', NEO_PROJECTS);
    NEO_PROJECTS.forEach(project => {
        const VideoFolder = `./Videos/${project}`;
        console.log(`\nProcessing project: ${project}`);
        console.log(`Video folder: ${VideoFolder}`);
        processSubtasks(parentTicketId, VideoFolder);
    });
} else {
    // Handle single project as before
    console.log(`Using project: ${projectName}`);
    const VideoFolder = `./Videos/${projectName}`;
    processSubtasks(parentTicketId, VideoFolder);
}
