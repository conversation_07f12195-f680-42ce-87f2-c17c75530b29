//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';
import { createStandardUser } from './api_helpers/orgUsers';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Location - open and check elements', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Location'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Check location field is shown and has default value of Wales', async () => {
        //The only way to ID this field is by the aria label, which includes the current location
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toBeVisible();
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toContainText('Wales');
    });

    await test.step('Click the location picker and check the elements', async () => {
        await page.locator(`[aria-label="Location Wales expandable"]:visible`).click();
        await expect(page.getByLabel('expanded', { exact: true }).getByText('Wales')).toBeVisible();

        await expect(page.getByText('Test Building 1')).toBeVisible();
        await expect(page.getByText('England')).toBeVisible();
        await expect(page.getByRole('button', { name: 'Done' })).toBeVisible();
        //Elements checked below only apply to small view
        if (projectViewport.width < 768){
        await expect(page.getByRole('heading', { name: 'Select location' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
        }
    });
    
    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-search-location.png');
    });

});

test('Location - expand and collapse', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Location'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Check location field is shown and has default value of Wales', async () => {
        //The only way to ID this field is by the aria label, which includes the current location
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toBeVisible();
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toContainText('Wales');
    });

    await test.step('Open the location filter and expand the location Test Building 1', async () => {
        await page.locator(`[aria-label="Location Wales expandable"]:visible`).click();
        await page.getByRole('treeitem', { name: 'Test Building 1' }).click();
    });

    await test.step('Check child locations are presented', async () => {
        await expect(page.getByText('Basement')).toBeVisible();
        await expect(page.getByText('Ground Floor')).toBeVisible();
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-search-location-expanded1level.png');
    });

    await test.step('Expand one further level (Ground floor) and check child locations are presented', async () => {
        await page.getByRole('treeitem', { name: 'Ground Floor' }).click();
        await expect(page.getByText('Desk Bank 1')).toBeVisible();
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-search-location-expanded2level.png');
    });

    await test.step('Validate the chevrons are shown at the relevant levels', async () => {
        await expect(page.getByRole('treeitem', { name: 'Test Building 1' }).locator('[class="size-6 max-w-fit"]').first()).toBeVisible();
        await expect(page.getByRole('treeitem', { name: 'Ground Floor' }).locator('[class="size-6 max-w-fit"]').first()).toBeVisible();
        await expect(page.getByRole('treeitem', { name: 'Desk Bank 1' }).locator('[class="size-6 max-w-fit"]')).toBeHidden();
    });
    
    await test.step('Collapse back to the base parent by clicking Wales', async () => {
        await page.locator('.size-6').first().click();
        await expect(page.getByText('Basement')).toBeHidden();
        await expect(page.getByText('Ground Floor')).toBeHidden();
        await expect(page.getByText('Desk Bank 1')).toBeHidden();
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-search-location-collapsedwales.png');
    });

});

test('Location - select a new location', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Location'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Check location field is shown and has default value of Wales', async () => {
        //The only way to ID this field is by the aria label, which includes the current location
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toBeVisible();
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toContainText('Wales');
    });

    await test.step('Open the location filter select Test Building 1', async () => {
        await page.locator(`[aria-label="Location Wales expandable"]:visible`).click();
        await page.getByRole('treeitem', { name: 'Test Building 1' }).click();
    });

    await test.step('Check the background colour and border of the selected location (Test Building 1) is correct', async () => {
        const newSelection = await page.getByLabel('expanded', { exact: true }).locator('div').filter({ hasText: 'Test Building 1' }).first()

        //await page.getByLabel('expanded', { exact: true }).locator('div').filter({ hasText: 'Test Building' }).nth(1).click();

        await expect(newSelection).toHaveCSS('background-color','rgb(214, 234, 255)');
        await expect(newSelection).toHaveCSS('border-top-color','rgb(192, 193, 208)');
        await expect(newSelection).toHaveCSS('border-bottom-color','rgb(192, 193, 208)');
        await expect(newSelection).toHaveCSS('border-right-color','rgb(192, 193, 208)');
        await expect(newSelection).toHaveCSS('border-left-color','rgb(192, 193, 208)');
    });

    await test.step('Check the background colour of the parent location (Wales) is correct', async () => {
        const parentLocation = await page.getByLabel('expanded', { exact: true }).locator('div').filter({ hasText: 'Wales' }).first()
        await expect(parentLocation).toHaveCSS('background-color','rgb(235, 246, 255)');
    });

    await test.step('Compare new selection against golden file', async () => {
        await expect.soft(page.getByRole('treeitem', { name: 'Test Building 1' })).toHaveScreenshot('Neo-new-booking-search-location-selecteditemintree.png');
    });
    
    await test.step('Press Done and validate the location filter shows the expected value', async () => {
        await page.getByRole('button', { name: 'Done' }).click();
        await expect(page.locator(`[aria-label="Location Test Building 1, Wales expandable"]:visible`)).toBeVisible();

        await expect(page.locator(`[aria-label="Location Test Building 1, Wales expandable"]:visible`)).toContainText('Test Building 1, Wales');
    });

    await test.step('Compare location filter after completed selection against golden file', async () => {
        await expect.soft(page.locator(`[aria-label="Location Test Building 1, Wales expandable"]:visible`)).toHaveScreenshot('Neo-new-booking-search-location-newvalue.png');
    });

});    

test('Location - new location selection persists on refresh', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Location'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Check location field is shown and has default value of Wales (due to org hierarchy)', async () => {
        //The only way to ID this field is by the aria label, which includes the current location
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toBeVisible();
        await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toContainText('Wales');
    });

    await test.step('Open the location filter select Test Building 1', async () => {
        await page.locator(`[aria-label="Location Wales expandable"]:visible`).click();
        await page.getByRole('treeitem', { name: 'Test Building 1' }).click();
    });
    
    await test.step('Press Done and validate the location filter shows the expected value', async () => {
        await page.getByRole('button', { name: 'Done' }).click();
        await expect(page.locator(`[aria-label="Location Test Building 1, Wales expandable"]:visible`)).toBeVisible();
        await expect(page.locator(`[aria-label="Location Test Building 1, Wales expandable"]:visible`)).toContainText('Test Building 1, Wales');
    });

    await test.step('Refresh the browser and ensure the location is still the new value', async () => {
        await page.reload();
        await expect(page.locator(`[aria-label="Location Test Building 1, Wales expandable"]:visible`)).toBeVisible();
        await expect(page.locator(`[aria-label="Location Test Building 1, Wales expandable"]:visible`)).toContainText('Test Building 1, Wales');
    });

});

test('Location - default location set to non bookable resource', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Location - Default'} }, async ({ request, baseURL, page }) => {

    const standard_user_data = await createStandardUser(request, baseURL);

    //Please note: we are having to utilise the current web app to set the user's default location as this does not exist in Neo yet
    await test.step('Login to the current web app & set the users location to England', async () => {
        await page.goto('ui/#/profile?view=preferences');
        await page.getByLabel('Email').fill(standard_user_data.Email);
        await page.getByLabel('Password').fill(standard_user_data.Password);
        await page.getByRole('button', { name: 'Log in' }).click();
        await page.locator('tags-input').getByPlaceholder('Location (optional)').fill('England');
        await page.getByRole('button', { name: 'England', exact: true }).locator('a').click();
        await expect(page.getByText('England', { exact: true }).first()).toBeVisible();
        await page.getByRole('button', { name: 'Save' }).click();
        await expect(page.locator('#main-content')).toContainText('Your preferences have been saved.');
    });

    await test.step('Navigate to the Neo search page', async () => {
        await page.goto('/neo/search');
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
    });

    await test.step('Check the location field is showing England as this is now the users default location', async () => {
        //The only way to ID this field is by the aria label, which includes the current location
        await expect.soft(page.locator(`[aria-label="Location England expandable"]:visible`)).toBeVisible();
        await expect.soft(page.locator(`[aria-label="Location England expandable"]:visible`)).toContainText('England');
    });

});

test('Location - default location set to a bookable resource', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Location - Default'} }, async ({ request, baseURL, page }) => {

    const standard_user_data = await createStandardUser(request, baseURL);

    //Please note: we are having to utilise the current web app to set the user's default location as this does not exist in Neo yet
    await test.step('Login to the current web app & set the users location to England Basement Room 1', async () => {
        await page.goto('ui/#/profile?view=preferences');
        await page.getByLabel('Email').fill(standard_user_data.Email);
        await page.getByLabel('Password').fill(standard_user_data.Password);
        await page.getByRole('button', { name: 'Log in' }).click();
        await page.locator('tags-input').getByPlaceholder('Location (optional)').fill('England Basement Room 1');
        await page.getByRole('button', { name: 'England Basement Room 1 – England Basement, England Test Building 1', exact: true }).locator('a').click();
        await expect(page.getByText('England Basement Room 1', { exact: true })).toBeVisible();
        await page.getByRole('button', { name: 'Save' }).click();
        await expect(page.locator('#main-content')).toContainText('Your preferences have been saved.');
    });

    await test.step('Navigate to the Neo search page', async () => {
        await page.goto('/neo/search');
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
    });

    await test.step('Check the location field is showing the floor that England Basement Room 1 is on (England Basement)', async () => {
        //The only way to ID this field is by the aria label, which includes the current location
        await expect.soft(page.locator(`[aria-label="Location England Basement, England Test Building 1 expandable"]:visible`)).toBeVisible();
        await expect.soft(page.locator(`[aria-label="Location England Basement, England Test Building 1 expandable"]:visible`)).toContainText('England Basement');
    });

});

test('Location - default location set to multiple non bookable resources', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Location - Default'} }, async ({ request, baseURL, page }) => {

    const standard_user_data = await createStandardUser(request, baseURL);

    //Please note: we are having to utilise the current web app to set the user's default location as this does not exist in Neo yet
    await test.step('Login to the current web app & set the users location to England Ground Floor and Ground Floor', async () => {
        await page.goto('ui/#/profile?view=preferences');
        await page.getByLabel('Email').fill(standard_user_data.Email);
        await page.getByLabel('Password').fill(standard_user_data.Password);
        await page.getByRole('button', { name: 'Log in' }).click();
        await page.locator('tags-input').getByPlaceholder('Location (optional)').fill('England Ground Floor');
        await page.getByRole('button', { name: 'England Ground Floor – England Test Building 1', exact: true }).locator('a').click();
        await expect(page.getByText('England Ground Floor', { exact: true })).toBeVisible();
        await page.locator('tags-input').getByPlaceholder('Location (optional)').fill('Ground Floor');
        await page.getByRole('button', { name: 'Ground Floor – Test Building 1', exact: true }).locator('a').click();
        await expect(page.getByText('Ground Floor', { exact: true })).toBeVisible();
        await page.getByRole('button', { name: 'Save' }).click();
        await expect(page.locator('#main-content')).toContainText('Your preferences have been saved.');
    });

    let apiResponse;

    await test.step('Navigate to the Neo search page', async () => {
        await page.goto('/neo/search');
        //Capture the user's details when navigating over to neo
        apiResponse = await page.waitForResponse(response =>
        response.url().includes('/api/v1/user/current?include=defaults') && response.status() === 200);
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
    });

    await test.step('Check the location field is showing Ground Floor (should select first provided from the BE)', async () => {
        //Below we are having to jump through some hoops as the BE seems to mix up the first location it wants to return for a user. 
        //The point is that the FE just uses the first one and does not do any ordering itself
        const data = await apiResponse.json();
        const firstLocation = data.defaults.searchLocations[0].name;
        
        if (firstLocation === "Ground Floor"){
            await expect.soft(page.locator(`[aria-label="Location Ground Floor, Test Building 1 expandable"]:visible`)).toBeVisible();
            await expect.soft(page.locator(`[aria-label="Location Ground Floor, Test Building 1 expandable"]:visible`)).toContainText('Ground Floor');
        } else {
            await expect.soft(page.locator(`[aria-label="Location England Ground Floor, England Test Building 1 expandable"]:visible`)).toBeVisible();
            await expect.soft(page.locator(`[aria-label="Location Ground Floor, England Test Building 1 expandable"]:visible`)).toContainText('Ground Floor');
        }

    });

});