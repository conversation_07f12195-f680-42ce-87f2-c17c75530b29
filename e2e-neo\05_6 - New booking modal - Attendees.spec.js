//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check attendees section', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Add attendees'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check the attendee section elements are present', async () => {
        
        if (projectViewport.width < 768){
            await expect(newBookingModalPage.mobileAddAttendeesButton).toBeVisible();
            await newBookingModalPage.mobileAddAttendeesButton.click();
            await expect(page.getByRole('heading', { name: 'Add attendees' })).toBeVisible();
            await expect(page.getByText('Search for attendee')).toBeVisible();
            await expect(newBookingModalPage.mobileSearchField).toBeVisible();
            await expect(page.getByRole('heading', { name: 'Attendees', exact: true })).toBeVisible();
        } else {
            await expect(page.getByRole('heading', { name: 'Attendees' })).toBeVisible();
            await expect(page.getByText('Add attendee', { exact: true })).toBeVisible();
            await expect(page.getByPlaceholder('Search by name or email')).toBeVisible();
        }
        
        await expect(newBookingModalPage.createNewAttendeeButton).toBeVisible();
        await expect(page.getByText('Number of attendees')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('1');

    });

    await test.step('Check current user is listed as an attendee and owner', async () => {
        
        if (projectViewport.width < 768){
            await expect(page.getByLabel('Add attendees').getByText(`${standard_user_data.FirstName} ${standard_user_data.LastName} (owner)`)).toBeVisible();
            await expect(page.getByLabel('Add attendees').getByText(`${standard_user_data.Email}`)).toBeVisible();
        } else {
            await expect(page.getByText(`${standard_user_data.FirstName} ${standard_user_data.LastName} (owner)`)).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByText(`${standard_user_data.Email}`)).toBeVisible();
        }

    });
    
    await test.step('Compare the attendee section against golden file', async () => {

        if (projectViewport.width < 768){
            await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-attendees.png');
        } else {
            await expect.soft(page.getByText('AttendeesNumber of attendees')).toHaveScreenshot('Neo-new-booking-modal-attendees.png');
        }

    });

});

test('Search valid internal attendee', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Add attendees'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Search for a valid internal attendee using first name & check expected use is list as an option', async () => {
        
        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
            await newBookingModalPage.mobileSearchField.fill(email_user_data.FirstName);

        } else {
            await page.getByPlaceholder('Search by name or email').fill(email_user_data.FirstName);
        }
        
        await expect(page.getByRole('option', {name: email_user_data.Name})).toBeVisible();
        await expect(page.getByText(email_user_data.Email)).toBeVisible();
        
    });

    await test.step('Compare the search against golden file', async () => {

        if (projectViewport.width < 768){
            await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-searchattendees.png');
        } else {
            await expect.soft(page.getByText('AttendeesNumber of attendees')).toHaveScreenshot('Neo-new-booking-modal-searchattendees.png');
        }

    });

    await test.step('Clear search and perform same actions using email', async () => {
        
        if (projectViewport.width < 768){
            await newBookingModalPage.mobileSearchField.fill('');
            await expect(page.getByRole('option', {name: email_user_data.Name})).toBeHidden();
            await newBookingModalPage.mobileSearchField.fill(email_user_data.Email);
        } else {
            await page.getByPlaceholder('Search by name or email').fill('');
            await expect(page.getByRole('option', {name: email_user_data.Name})).toBeHidden();
            await page.getByPlaceholder('Search by name or email').fill(email_user_data.Email);
        }
        
        await expect(page.getByRole('option', {name: email_user_data.Name})).toBeVisible();
        await expect(page.getByText(email_user_data.Email)).toBeVisible();
        
    });

});

test('Add attendee', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Add attendees'} }, async ({ page, loginPage,  newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Search for a internal attendee & select the correct option', async () => {
        
        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
            await newBookingModalPage.mobileSearchField.fill(email_user_data.FirstName);

        } else {
            await page.getByPlaceholder('Search by name or email').fill(email_user_data.FirstName);
        }
        
        await page.getByRole('option', {name: email_user_data.Name}).click();
    });

    await test.step('Validate the selected attendee is now listed and the attendee count has increased by one', async () => {
        
        if (projectViewport.width < 768){
            await expect(page.getByLabel('Add attendees').locator('li').filter({ hasText: email_user_data.Name })).toBeVisible();
            await expect(page.getByLabel('Add attendees').getByText(email_user_data.Name)).toBeVisible();
            await expect(page.getByLabel('Add attendees').getByText(email_user_data.Email)).toBeVisible();
            await expect(page.getByLabel('Add attendees').locator('li').filter({ hasText: email_user_data.Name }).getByLabel('Remove attendee')).toBeVisible();
            await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-addattendeesmall.png');
            await page.getByRole('button', { name: 'Done' }).click();
        }

        await expect(page.locator('li').filter({ hasText: email_user_data.Name })).toBeVisible();
        await expect(page.getByText(email_user_data.Name)).toBeVisible();
        await expect(page.getByText(email_user_data.Email)).toBeVisible();
        await expect(page.locator('li').filter({ hasText: email_user_data.Name }).getByLabel('Remove attendee')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('2');
    });

    await test.step('Compare the attendee list against golden file', async () => {

        if (projectViewport.width < 768){
            await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-addattendee.png');
        } else {
            await expect.soft(page.getByText('AttendeesNumber of attendees')).toHaveScreenshot('Neo-new-booking-modal-addattendee.png');
        }

    });

});

test('Remove attendee', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Remove attendees'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Search for a internal attendee & select the correct option', async () => {
        
        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
            await newBookingModalPage.mobileSearchField.fill(email_user_data.FirstName);
        } else {
            await page.getByPlaceholder('Search by name or email').fill(email_user_data.FirstName);
        }
        
        await page.getByRole('option', {name: email_user_data.Name}).click();

        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Done' }).click();
        }

        await expect(page.locator('li').filter({ hasText: email_user_data.Name })).toBeVisible();
        await expect(page.locator('li').filter({ hasText: email_user_data.Name }).getByLabel('Remove attendee')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('2');

    });

    await test.step('Remove the newly added attendee', async () => {
        page.locator('li').filter({ hasText: email_user_data.Name }).getByLabel('Remove attendee').click();
        await expect(page.locator('li').filter({ hasText: email_user_data.Name })).toBeHidden();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('1');
    });

});

test('Requires attendee count NOT enabled', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Number of attendees'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Validate Number of attendees field is presented and is prepoulated with a value', async () => {
        await expect(page.getByText('Number of attendees')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('1');
    });
    
    await test.step('Validate Number of attendees field is enabled and the value can be changed', async () => {
        await expect(page.getByLabel('Number of attendees')).toBeEnabled();
        await expect(page.getByLabel('Number of attendees')).toBeEditable();
        await page.getByLabel('Number of attendees').fill('99');
        await expect(page.getByLabel('Number of attendees')).toHaveValue('99');
    });
    
});

test('Requires attendee count is enabled', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Number of attendees'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 6 - Mandatory fields', async () => {
        await page.getByRole('button', { name: 'Basement Room 6 - Mandatory fields' }).click();
    });

    await test.step('Validate Number of attendees field is presented and is NOT prepoulated with a value', async () => {
        await expect(page.getByText('Number of attendees')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('');
    });
    
    await test.step('Validate Number of attendees field is enabled and the value can be changed', async () => {
        await expect(page.getByLabel('Number of attendees')).toBeEnabled();
        await expect(page.getByLabel('Number of attendees')).toBeEditable();
        await page.getByLabel('Number of attendees').fill('99');
        await expect(page.getByLabel('Number of attendees')).toHaveValue('99');
    });
    
});

test('Check add external attendee form', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Add new external attendee'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check click Create new attendee & check the attendee details form for expected elements', async () => {

        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
        }

        await newBookingModalPage.createNewAttendeeButton.click();
        await expect(page.getByText('Attendee details')).toBeVisible();
        await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
        await expect(page.getByLabel('Attendee details').getByText('All fields marked with * are')).toBeVisible();
        
        //Below are the labels for the fields on the attendee details modal. It is unlikely these will need to be referenced in any other tests.
        if (projectViewport.width < 768){
            await expect(page.locator('[id="headlessui-label-\\:r49\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r49\\:"]')).toContainText('First name *');
            await expect(page.locator('[id="headlessui-label-\\:r4c\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r4c\\:"]')).toContainText('Last name *');
            await expect(page.locator('[id="headlessui-label-\\:r4f\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r4f\\:"]')).toContainText('Company');
            await expect(page.locator('[id="headlessui-label-\\:r4i\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r4i\\:"]')).toContainText('Email');
            await expect(page.locator('[id="headlessui-label-\\:r4r\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r4r\\:"]')).toContainText('Phone number');
            await expect(page.locator('[id="headlessui-label-\\:r4u\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r4u\\:"]')).toContainText('Car registration');
        } else {
            await expect(page.locator('[id="headlessui-label-\\:r3p\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r3p\\:"]')).toContainText('First name *');
            await expect(page.locator('[id="headlessui-label-\\:r3s\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r3s\\:"]')).toContainText('Last name *');
            await expect(page.locator('[id="headlessui-label-\\:r45\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r45\\:"]')).toContainText('Company');
            await expect(page.locator('[id="headlessui-label-\\:r48\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r48\\:"]')).toContainText('Email');
            await expect(page.locator('[id="headlessui-label-\\:r4b\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r4b\\:"]')).toContainText('Phone number');
            await expect(page.locator('[id="headlessui-label-\\:r4e\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r4e\\:"]')).toContainText('Car registration');
            await expect(page.getByRole('button', { name: 'Cancel' })).toBeVisible();
        }

        await expect(newBookingModalPage.adFirstnameField).toBeVisible();
        await expect(newBookingModalPage.adLastnameField).toBeVisible();
        await expect(newBookingModalPage.adCompanyField).toBeVisible();  
        await expect(newBookingModalPage.adEmailField).toBeVisible();
        await expect(newBookingModalPage.adPhoneNumField).toBeVisible();
        await expect(newBookingModalPage.adCarRegField).toBeVisible();
        await expect(page.getByRole('button', { name: 'Done' })).toBeVisible();
    });
    
    await test.step('Compare the attendee details form against a golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-addExternalAttendeesForm.png');
    });

});

test('Close external attendee form early', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Add new external attendee'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check click Create new attendee', async () => {

        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
        }

        await newBookingModalPage.createNewAttendeeButton.click();
    });

    await test.step('Enter some details then click cancel or close', async () => {
        await newBookingModalPage.adFirstnameField.fill('CancelTestFirstname');
        await newBookingModalPage.adLastnameField.fill('CancelTestLastname');

        if (projectViewport.width < 768 ){
            await page.getByRole('button', { name: 'Close panel' }).click();
        } else {
            await page.getByRole('button', { name: 'Cancel' }).click();
        }

    });

    await test.step('Check that the attendee has NOT been added tot he attendee section', async () => {
        await expect(page.locator('li').filter({ hasText: `CancelTestFirstname CancelTestLastname` })).toBeHidden();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('1');
    });

});

test('Check mandatory fields on external attendee form', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Add new external attendee'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check click Create new attendee', async () => {

        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
        }

        await newBookingModalPage.createNewAttendeeButton.click();
    });

    await test.step('Click done with no details entered and check error message', async () => {
        await page.getByRole('button', { name: 'Done' }).click();
        await expect(page.getByRole('button', { name: 'Done' })).toBeVisible();
        await expect(page.getByRole('alert')).toBeVisible();
        await expect(page.getByRole('alert')).toContainText('Error creating attendeeFirst Name and Last Name are required.');
    });

    await test.step('Compare the error against a golden file', async () => {
        await expect.soft(page.getByRole('alert')).toHaveScreenshot('Neo-new-booking-modal-addExternalAttendeesFormError.png');
        await expect(page.getByRole('alert')).toMatchAriaSnapshot(`
          - alert:
            - heading "Error creating attendee" [level=3]
            - text: First Name and Last Name are required.
          `);
    });

});

test('Add external attendee to booking form', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Add new external attendee'} }, async ({ baseURL, page, loginPage, newBookingModalPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check click Create new attendee', async () => {

        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
        }

        await newBookingModalPage.createNewAttendeeButton.click();
    });

    const timeNow = dayjs().format('DDMMHHmmssSSS');

    const eaDetails = ({
        "Email": `externalattendee${timeNow}@notanemail123.com`,
        "FirstName":`Firstname${timeNow}`,
        "LastName":`Lastname${timeNow}`,
        "Company" : "CompanyName",
        "PhoneNumber":"01792800300",
        "CarReg":"KU56CXZ"
    });

    await test.step('Enter all details to create an external attendee', async () => {
        await newBookingModalPage.adFirstnameField.fill(eaDetails.FirstName);
        await newBookingModalPage.adLastnameField.fill(eaDetails.LastName);
        await newBookingModalPage.adCompanyField.fill(eaDetails.Company);
        await newBookingModalPage.adEmailField.fill(eaDetails.Email);
        await newBookingModalPage.adPhoneNumField.fill(eaDetails.PhoneNumber);
        await newBookingModalPage.adCarRegField.fill(eaDetails.CarReg);
        await page.getByRole('button', { name: 'Done' }).click();
    });

    await test.step('Check that the external attendee has been added to the attendee section of the booking', async () => {
        
        if (projectViewport.width < 768){
            await expect(page.getByLabel('Add attendees').locator('li').filter({ hasText: `${eaDetails.FirstName} ${eaDetails.LastName}` })).toBeVisible();
            await expect(page.getByLabel('Add attendees').getByText(`${eaDetails.FirstName} ${eaDetails.LastName}`)).toBeVisible();
            await expect(page.getByLabel('Add attendees').getByText(`${eaDetails.Email}`)).toBeVisible();
            await page.getByRole('button', { name: 'Done' }).click();
        }
        
        await expect(page.locator('li').filter({ hasText: `${eaDetails.FirstName} ${eaDetails.LastName}` })).toBeVisible();
        await expect(page.locator('li').filter({ hasText: `${eaDetails.Email}` })).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toHaveValue('2');
    });

    await test.step('Check the attendee gets added as an external contact to the org', async () => {
        //I have set this up tp be validate by an api call, rather than login an admin etc and check via the old UI
        //Maybe this will change when we have a more responsive admin area
        const apiResponse = await page.request.get(`${baseURL}api/v1/person?partial=${eaDetails.FirstName} ${eaDetails.LastName}&kind=external`);
        const data = await apiResponse.json();
        expect(data[0].company).toBe(eaDetails.Company);
        expect(data[0].contactNumber).toBe(eaDetails.PhoneNumber);
        expect(data[0].email).toBe(eaDetails.Email);
        expect(data[0].firstName).toBe(eaDetails.FirstName);
        expect(data[0].isEnabled).toBe(true);
        expect(data[0].isUser).toBe(false);
        expect(data[0].kind).toBe("EXTERNAL");
        expect(data[0].lastName).toBe(eaDetails.LastName);
        expect(data[0].name).toBe(`${eaDetails.FirstName} ${eaDetails.LastName}`);
        expect(data[0].vehicleRegistration).toBe(eaDetails.CarReg);
        expect(data[0].vehicleType).toBe("CAR");
    });

});