//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check layout section is presented where configured', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Layout'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

     await test.step('Validate that NO layout is displayed as it is not congiured for this resource', async () => { 
        await expect(page.getByRole('heading', { name: 'Options' })).toBeHidden();
        await expect(newBookingModalPage.roomLayoutButton).toBeHidden();
        await page.getByRole('button', { name: 'Close panel' }).click();
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });
    
    await test.step('Validate that layout section is displayed for a resource where it is configured', async () => { 
        await expect(page.getByRole('heading', { name: 'Options' })).toBeVisible();
        await expect(newBookingModalPage.roomLayoutButton).toBeVisible();
    });
 
});

test('Open the layout accordian and click select one', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Layout'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 8 - Layout', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 8 - Layout', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 8 - Layout', exact: true }).click();
    });
    
    await test.step('Click the option to expand its accordian', async () => {
        await newBookingModalPage.roomLayoutButton.click();
    });

    await test.step('Check the expected elements are presented', async () => {
        await expect(page.getByLabel('Banquet')).toBeVisible();
        await expect(page.getByText('Banquet')).toBeVisible();
        await expect(page.getByLabel('Buffet')).toBeVisible();
        await expect(page.getByText('Buffet')).toBeVisible();
        await expect(page.getByLabel('Cabaret')).toBeVisible();
        await expect(page.getByText('Cabaret')).toBeVisible();
    });
    
    await test.step('Compare layout section with no layout selected against a golden file image', async () => {
        await expect.soft(page.getByText('Room layoutBanquetBuffetCabaret')).toHaveScreenshot('Neo-bookingModal-LayoutsUnselected.png');
    });

    await test.step('Click a layout, validate selection and add request button', async () => { 
        await page.getByLabel('Buffet').click();
        await expect(page.locator('[class="border-border-highlight bg-border-highlight w-fit cursor-pointer rounded border"]')).toHaveCSS('background-color','rgb(15, 101, 196)');
        await expect(page.locator('[class="block rounded focus-visible:z-10 focus-visible:outline focus-visible:outline-denim focus-visible:outline-[2px] focus-visible:outline-offset-2"]').nth(1)).toBeChecked();
        await expect(page.getByRole('button', { name: 'Add request note Add request' })).toBeVisible();
    });

    await test.step('Compare layout section with selected layout against a golden file image', async () => {
        await expect.soft(page.getByText('Room layoutBanquetBuffetCabaretAdd request note')).toHaveScreenshot('Neo-bookingModal-LayoutsSelected.png');
    });

    await test.step('Click a different layout, validate it is select & check it is single select', async () => {
        await page.getByLabel('Cabaret').click();
        await expect(page.locator('[class="block rounded focus-visible:z-10 focus-visible:outline focus-visible:outline-denim focus-visible:outline-[2px] focus-visible:outline-offset-2"]').nth(2)).toBeChecked();
        await expect(page.locator('[class="block rounded focus-visible:z-10 focus-visible:outline focus-visible:outline-denim focus-visible:outline-[2px] focus-visible:outline-offset-2"]').nth(1)).toBeChecked({checked:false});
    });

    await test.step('Click add notes & validate', async () => {
        await page.getByRole('button', { name: 'Add request note Add request' }).click();
        await expect(page.getByText('Request note',{exact:true})).toBeVisible();
        await expect(page.getByLabel('Request note',{exact:true})).toBeVisible();
        await expect(page.getByLabel('Layouts').getByRole('button', { name: 'Cancel' })).toBeVisible();
        await page.getByLabel('Request note').fill('TestString@#$%^&*()👍😊こんにちは');
        await expect(page.getByLabel('Request note')).toHaveValue('TestString@#$%^&*()👍😊こんにちは');
    });

    await test.step('Compare layout section with selected layout against a golden file image', async () => {
        await expect.soft(page.getByText('Room layoutBanquetBuffetCabaretRequest note TestString@#$%^&*()👍😊こんにちはCancel')).toHaveScreenshot('Neo-bookingModal-LayoutsWithNote.png');
    });

    await test.step('Click cancel below the note', async () => {
        await page.getByLabel('Layouts').getByRole('button', { name: 'Cancel' }).click();
        await expect(page.getByRole('button', { name: 'Add request note Add request' })).toBeVisible();
        await expect(page.getByText('Request note',{exact:true})).toBeHidden();
        await expect(page.getByLabel('Request note',{exact:true})).toBeHidden();
        await expect(page.getByLabel('Layouts').getByRole('button', { name: 'Cancel' })).toBeHidden();
    });

    await test.step('Click the same layout again and validate no layout is selected now', async () => {
        await page.getByLabel('Cabaret').click();
        await expect(page.locator('[class="block rounded focus-visible:z-10 focus-visible:outline focus-visible:outline-denim focus-visible:outline-[2px] focus-visible:outline-offset-2"]').nth(2)).toBeChecked({checked:false});
        await expect(page.getByRole('button', { name: 'Add request note Add request' })).toBeHidden();
    });
      
});