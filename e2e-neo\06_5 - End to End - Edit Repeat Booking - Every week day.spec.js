//import test and expect functionality
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to end - Edit repeat booking - Every week day', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Edit repeat booking - Every week day' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(email_user_data);
    });

    // Create initial booking for testing edits
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2, 'hour');
    const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
    const bookingTitle = `Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    const repeatUntilDate = dayjs(bookingFromTime).add(2, 'week');

    // Create a basic repeat booking for testing edits
    await test.step('Create initial repeat booking', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await searchResultsPage.bookResource('Basement Room 2');
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
        await newBookingModalPage.setupRepeatBooking('Every week day', repeatUntilDate, projectViewport.width);
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.completeBooking();
        await expect.soft(page).toHaveScreenshot('Neo-edit-repeat-initial-booking.png');
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    // Edit details for single occurrence
    const editedSingleTitle = `${bookingTitle} - Single Edit`;
    const editedSingleFromTime = dayjs(bookingFromTime).add(1, 'hour');
    const editedSingleToTime = dayjs(editedSingleFromTime).add(30, 'minutes');
    
    await test.step('Edit single occurrence of repeat booking', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        await expect(newBookingModalPage.modalHeadingEdit).toContainText('Edit booking');
        await expect.soft(page).toHaveScreenshot('Neo-edit-repeat-single-before.png');
        
        await newBookingModalPage.setStartTime(editedSingleFromTime);
        await newBookingModalPage.setEndTime(editedSingleToTime);
        await newBookingModalPage.setTitle(editedSingleTitle);
        await expect.soft(page).toHaveScreenshot('Neo-edit-repeat-single-after.png');
        
        await newBookingModalPage.saveButton.click();
        await newBookingModalPage.bookingSuccessIcon.waitFor({ state: 'visible' });
        await expect.soft(page).toHaveScreenshot('Neo-edit-repeat-single-success.png');
        
        //click back to bookings button
        await newBookingModalPage.goToBookingsList(projectViewport.width);
        
        // Verify single occurrence was updated
        await myBookingsPage.verifyBookingExists(editedSingleFromTime, editedSingleToTime, editedSingleTitle, projectViewport.width);
        
        // Verify next occurrence remains unchanged
        const nextDayBooking = dayjs(bookingFromTime).add(1, 'day');
        await myBookingsPage.verifyBookingExists(nextDayBooking, dayjs(nextDayBooking).add(30, 'minutes'), bookingTitle, projectViewport.width);
    });
    
    // Edit details for all remaining occurrences
    const editedSeriesTitle = `${bookingTitle} - Series Edit`;
    const editedSeriesFromTime = dayjs(bookingFromTime).add(2, 'hour');
    const editedSeriesToTime = dayjs(editedSeriesFromTime).add(30, 'minutes');
    
    await test.step('Edit all remaining occurrences of repeat booking', async () => {
        const nextBookingDate = dayjs(bookingFromTime).add(2, 'day');
        await myBookingsPage.editBooking(nextBookingDate, bookingToTime, bookingTitle, projectViewport.width);
        await expect.soft(page).toHaveScreenshot('Neo-edit-repeat-series-before.png');
        
        await page.getByText('Edit all remaining bookings').click();
        //start and end time shouldnt be shown
        await expect(newBookingModalPage.startTimeField).toBeHidden();
        await expect(newBookingModalPage.endTimeField).toBeHidden();
        //title should be shown
        await newBookingModalPage.setTitle(editedSeriesTitle);
        await expect.soft(page).toHaveScreenshot('Neo-edit-repeat-series-after.png');
        
        await newBookingModalPage.saveButton.click();
        await newBookingModalPage.bookingSuccessIcon.waitFor({ state: 'visible' });    
        await expect.soft(page).toHaveScreenshot('Neo-edit-repeat-series-success.png');
        
        //click back to bookings button
        await newBookingModalPage.goToBookingsList(projectViewport.width);
        
        // Verify remaining occurrences were updated
        let currentDate = dayjs(nextBookingDate);
        while (currentDate.isBefore(repeatUntilDate) || currentDate.isSame(repeatUntilDate, 'day')) {
            if (currentDate.day() !== 0 && currentDate.day() !== 6) {
                await myBookingsPage.verifyBookingExists(
                    currentDate.hour(editedSeriesFromTime.hour()).minute(editedSeriesFromTime.minute()),
                    currentDate.hour(editedSeriesToTime.hour()).minute(editedSeriesToTime.minute()),
                    editedSeriesTitle,
                    projectViewport.width
                );
            }
            currentDate = currentDate.add(1, 'day');
        }
    });

    await test.step('Clean up - Cancel all repeat bookings', async () => {
        await myBookingsPage.goto();
        await page.waitForLoadState('networkidle');
        await myBookingsPage.cancelBooking(
            editedSeriesFromTime,
            editedSeriesToTime,
            editedSeriesTitle,
            true,
            projectViewport.width
        );
    });
});