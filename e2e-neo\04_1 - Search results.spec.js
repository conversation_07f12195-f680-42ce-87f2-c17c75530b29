//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check search results page', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen'}  }, async ({ baseURL, page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await newBookingSearchPage.searchButton.click();
    });

    await test.step('Validate the page elements are presented', async () => {
        await expect(page).toHaveTitle('Search results - Matrix Booking');

        //The URL of the search results page is volatile (has random numbers in it) so below is an attempt to validate
        const pagePath = 'neo/search/results';
        const expectedURL = new RegExp(`^${baseURL}${pagePath}\/\\d+\/\\d+\/\\d+\/\\d+\/\\d+\/\\d+\/\\d+\/-1\/-1$`);
        await expect(page).toHaveURL(expectedURL);

        await expect(page.getByRole('status')).toContainText('12 meeting rooms available');

        if (projectViewport.width > 1023){
            await expect(page.getByText('Filter byClear all')).toBeVisible();    
        }

    });

    await test.step('Check expected resources are listed', async () => {
        //PLEASE NOTE: This step may be a bad idea but ultimately the test org is fixed so *should* return the same resource each time
        await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeVisible();
        await expect(page.locator('#main')).toContainText('Basement Room 1');
        await expect(page.locator('#main')).toContainText('Basement Room 3 - Cost code');
        await expect(page.locator('#main')).toContainText('Basement Room 4 - Alert');
        await expect(page.locator('#main')).toContainText('Basement Room 5 - Need approval');
        await expect(page.locator('#main')).toContainText('Basement Room 6 - Mandatory fields');
        await expect(page.locator('#main')).toContainText('Basement Room 7 - Facilities');
        await expect(page.locator('#main')).toContainText('Basement Room 8 - Layout');
        await expect(page.locator('#main')).toContainText('Ground Floor Room 1');
        await expect(page.locator('#main')).toContainText('Ground Floor Room 2');
    });
    
    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-searchresults-page.png');
    });
    
});

test('Check search criteria', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen'}  }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    let timeNow = dayjs();
    let next30minTimeSlot = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');

    await test.step('Perform a search', async () => {
        await newBookingSearchPage.searchButton.click();
    });

    await test.step('Validate the search criteria is present and has the expected values', async () => {
        await expect(page).toHaveTitle('Search results - Matrix Booking')
        await expect(page.getByRole('status')).toContainText('12 meeting rooms available');
        
        if (projectViewport.width > 1023){
            await expect(page.getByRole('button', { name: 'Resource type Meeting Rooms' })).toBeVisible();
            await expect(page.locator(`[aria-label="Date ${timeNow.format('ddd, D MMM')} expandable"]:visible`)).toBeVisible();
            await expect(page.locator(`[aria-label="Location Wales expandable"]:visible`)).toBeVisible();
            await expect(page.getByRole('combobox', { name: 'Start time' })).toBeVisible();
            await expect(page.getByRole('combobox', { name: 'Start time' })).toHaveValue(`${next30minTimeSlot.format('h:mm A')}`);
            await expect(page.getByRole('combobox', { name: 'End time' })).toBeVisible();
            await expect(page.getByRole('combobox', { name: 'End time' })).toHaveValue(`${next30minTimeSlot.add(1, 'hour').format('h:mm A')}`);
            await expect(page.getByRole('button', { name: 'Capacity Any' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Search' })).toBeVisible();
        }
        else
        {
            await expect(page.locator('p').filter({ hasText: 'Meeting Rooms' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Edit' })).toBeVisible();
            await expect(page.locator('p').filter({ hasText: `${timeNow.format('ddd, D MMM')}` })).toBeVisible();
            await expect(page.getByText(`${next30minTimeSlot.format('h:mm A')} – ${next30minTimeSlot.add(1, 'hour').format('h:mm A')}`)).toBeVisible();
        }

    });
    
    await test.step('Compare the search criteria against golden file & aria snapshot', async () => {
        if (projectViewport.width > 1023){
            await expect.soft(page.locator('#main')).toMatchAriaSnapshot(`
            - text: Choose Resource type
            - button "Resource type Meeting Rooms expandable"
            - text: Choose Date
            - button "Date ${timeNow.format('ddd, D MMM')} expandable"
            - combobox "Start time"
            - text: Start time
            - button "Start time Select a time"
            - combobox "End time"
            - text: End time
            - button "End time Select a time"
            - text: Choose Capacity
            - button "Capacity Any expandable"
            - text: Choose Location
            - button "Location Wales expandable"
            - button "Search"
            `);
        await expect.soft(page.locator('[class="bg-bg-element px-4 pt-4 lg:pt-8 lg:pb-10"]')).toHaveScreenshot('Neo-searchResults-searchCriteria.png');
        }
        else
        {
            await expect.soft(page.locator('#main')).toMatchAriaSnapshot(`
              - paragraph: Meeting Rooms
              - button "Edit"
              - paragraph: ${timeNow.format('ddd, D MMM')}
              - paragraph: ${next30minTimeSlot.format('h:mm A')} – ${next30minTimeSlot.add(1, 'hour').format('h:mm A')}
              `);
            await expect.soft(page.locator('[class="flex flex-col  justify-center"]')).toHaveScreenshot('Neo-searchResults-searchCriteria.png');
        }
    });
    
});

test('Check page empty state', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Empty state'}  }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Change resource type to a unused type (Bedrooms currently) and perform a search', async () => {
        await newBookingSearchPage.resourceTypeField.click();
        await newBookingSearchPage.bedroomsOption.click();
        await newBookingSearchPage.searchButton.click();
    });

    await test.step('Validate there are no search results and no results message is presented', async () => {
        await expect(page).toHaveTitle('Search results - Matrix Booking')
        await expect(page.getByRole('heading', { name: 'No search results' })).toBeVisible();
        await expect(page.getByText('Try updating your search')).toBeVisible();
    });
    
    await test.step('Compare the empty page state against golden file & aria snapshot', async () => {
       await expect(page.locator('#main')).toMatchAriaSnapshot(`
          - heading "No search results" [level=1]
          - text: Try updating your search criteria
          `);
        await expect.soft(page).toHaveScreenshot('Neo-searchResults-emptyState.png');
    });
    
});

test('Check result resource card standard', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Card display'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await newBookingSearchPage.searchButton.click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    const basementRoom1Card = page.getByRole('heading', { name: 'Basement Room 1', exact: true }).locator('..').locator('..').locator('..').locator('..');

    await test.step('Check the resource card for Basement Room 1', async () => {
        await expect(basementRoom1Card).toBeVisible();
        await expect(basementRoom1Card).toContainText('Basement Room 1');
        await expect(basementRoom1Card).toContainText('Basement, Test Building 1');
        await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
    });
    
    await test.step('Compare card against golden file', async () => {
        await expect.soft(basementRoom1Card).toHaveScreenshot('Neo-searchresults-resourcecard1.png');
    });
    
});

test('Check result resource card with alert', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Card display'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await newBookingSearchPage.searchButton.click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    const basementRoom4Card = page.getByRole('heading', { name: 'Basement Room 4 - Alert' }).locator('..').locator('..').locator('..').locator('..');

    await test.step('Check the resource card for Basement Room 4 - Alert', async () => {
        await expect(basementRoom4Card).toBeVisible();
        await expect(basementRoom4Card).toContainText('Basement Room 4 - Alert');
        await expect(basementRoom4Card).toContainText('Basement, Test Building 1');
        await expect(basementRoom4Card.getByRole('button', { name: 'Book Basement Room 4 - Alert' })).toBeVisible();await expect(page.getByRole('button', { name: 'Book Basement Room 4 - Alert' })).toBeVisible();
    });
    
    await test.step('Compare card against golden file', async () => {
        await expect.soft(basementRoom4Card).toHaveScreenshot('Neo-searchresults-resourcecard4.png');
    });
    
});

test('Check result resource card with information', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Card display'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await newBookingSearchPage.searchButton.click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    const basementRoom5Card = page.getByRole('heading', { name: 'Basement Room 5 - Need approval' }).locator('..').locator('..').locator('..').locator('..');

    await test.step('Check the resource card for Basement Room 5', async () => {
        await expect(basementRoom5Card).toBeVisible();
        await expect(basementRoom5Card).toContainText('Basement Room 5 - Need approval');
        await expect(basementRoom5Card).toContainText('Basement, Test Building 1');
        await expect(basementRoom5Card.getByRole('img', { name: 'info' })).toBeVisible();
        await expect(basementRoom5Card.getByText('Approval required')).toBeVisible();
        await expect(basementRoom5Card.getByRole('button', { name: 'Book Basement Room 5' })).toBeVisible();
    });
    
    await test.step('Compare card against golden file', async () => {
        await expect.soft(basementRoom5Card).toHaveScreenshot('Neo-searchresults-resourcecard5.png');
    });
    
});

test('Check result resource card with facilities', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Card display - Facilities'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await newBookingSearchPage.searchButton.click();
        await expect(page).toHaveTitle('Search results - Matrix Booking')
    });

    const basementRoom7Card = page.getByRole('heading', { name: 'Basement Room 7 - Facilities' }).locator('..').locator('..').locator('..').locator('..');

    await test.step('Check the resource card Basement Room 7 - Facilities for 4 facilities', async () => {
        await expect(basementRoom7Card).toBeVisible();
        await expect(basementRoom7Card).toContainText('Basement Room 7 - Facilities');
        await expect(basementRoom7Card).toContainText('Basement, Test Building 1');
        await expect(basementRoom7Card.getByLabel('Adjustable Desk')).toBeVisible();
        await expect(basementRoom7Card.getByLabel('Air Conditioning')).toBeVisible();
        await expect(basementRoom7Card.getByLabel('Coffee Machine')).toBeVisible();
        await expect(basementRoom7Card.getByLabel('Computer')).toBeHidden();
        await expect(basementRoom7Card.getByRole('button', { name: '+1' })).toBeVisible();
        await expect(basementRoom7Card.getByRole('button', { name: 'Book Basement Room 7 - Facilities' })).toBeVisible();
    });

    const basementRoom6Card = page.getByRole('heading', { name: 'Basement Room 6 - Mandatory fields' }).locator('..').locator('..').locator('..').locator('..');
    await test.step('Check the resource card Basement Room 6 for 2 facilities', async () => {
        await expect(basementRoom6Card).toBeVisible();
        await expect(basementRoom6Card).toContainText('Basement Room 6 - Mandatory fields');
        await expect(basementRoom6Card).toContainText('Basement, Test Building 1');
        await expect(basementRoom6Card.getByLabel('Coffee Machine')).toBeVisible();
        await expect(basementRoom6Card.getByLabel('Computer')).toBeVisible();
        await expect(basementRoom6Card.getByRole('button', { name: '+' })).toBeHidden();
        await expect(basementRoom6Card.getByRole('button', { name: 'Book Basement Room 6' })).toBeVisible();
    });

    await test.step('Compare card against golden file', async () => {
        await expect.soft(basementRoom7Card).toHaveScreenshot('Neo-searchresults-resourcecard7.png');
    });

    await test.step('Click the +1 button to uncover the additional facility', async () => {
        await basementRoom7Card.getByRole('button', { name: '+1' }).click();
        await expect(basementRoom7Card.getByLabel('Computer')).toBeVisible();
    });

    await test.step('Compare card against golden file after revelaing additional facility', async () => {
        await expect.soft(basementRoom7Card).toHaveScreenshot('Neo-searchresults-resourcecard7-2.png');
    });
    
});

test('Check result resource cards with images', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Card display - Images'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await newBookingSearchPage.searchButton.click();
        await expect(page).toHaveTitle('Search results - Matrix Booking')
    });

    const basementRoom1Card = page.getByRole('heading', { name: 'Basement Room 1', exact: true }).locator('..').locator('..').locator('..').locator('..');

    await test.step('Check the resource card Basement Room 1 for a PNG Image', async () => {
        await expect(basementRoom1Card).toBeVisible();
        await expect(basementRoom1Card.getByRole('img')).toHaveAttribute('src', /.*TestPNG.*/);
    });

    await test.step('Compare card against golden file', async () => {
       await expect.soft(basementRoom1Card).toHaveScreenshot('Neo-searchresults-resourcecard1.png');
    });

    const basementRoom2Card = page.getByRole('heading', { name: 'Basement Room 2' }).locator('..').locator('..').locator('..').locator('..');

    await test.step('Check the resource card Basement Room 2 is showing the test JPG Image', async () => {
        await expect(basementRoom2Card).toBeVisible();    
        if (projectViewport.width > 767){
            await expect(basementRoom2Card.getByRole('img').nth(1)).toHaveAttribute('src', /.*TestJPG.*/)    
        } else{
            await expect(basementRoom2Card.getByRole('img').first()).toHaveAttribute('src', /.*TestJPG.*/) 
        }
    });  

    await test.step('Compare card against golden file', async () => {
        await expect.soft(basementRoom2Card).toHaveScreenshot('Neo-searchresults-resourcecard2.png');
    });

    const basementRoom3Card = page.getByRole('heading', { name: 'Basement Room 3 - Cost code' }).locator('..').locator('..').locator('..').locator('..');

    await test.step('Check the resource card Basement Room 3 for a SVG Image', async () => {
        await expect(basementRoom3Card).toBeVisible();
        if (projectViewport.width > 767){
            await expect(basementRoom3Card.getByRole('img')).toHaveAttribute('src', /.*TestSVG.*/)   
        } else{
            await expect(basementRoom3Card.getByRole('img')).toHaveAttribute('src', /.*TestSVG.*/) 
        }
    });

    await test.step('Compare card against golden file', async () => {
        await expect.soft(basementRoom3Card).toHaveScreenshot('Neo-searchresults-resourcecard3.png');
    });
    
});