export class LoginPage {

  /**
   * @param {import ('./fixtures/pages.fixture').Page} page 
   */

  constructor(page) {
    this.page = page;
    this.emailField = page.getByLabel('Email');
    this.passwordField = page.getByLabel('Password');
    this.loginButton = page.getByRole('button', { name: 'Log in' });
    this.forgottenPasswordLink = page.getByRole('link', { name: 'Forgot your password?' });
    this.logo = page.locator('img');
  }

   /**
   * @param {{ userData }} user
   * @returns {Promise<void>}
   */

  async simpleLogin(user) {
    await this.goto();
    await this.emailField.fill(user.Email);
    await this.passwordField.fill(user.Password);
    await this.loginButton.click();
  }

  async goto() {
    await this.page.goto('/neo/login');
  }

}