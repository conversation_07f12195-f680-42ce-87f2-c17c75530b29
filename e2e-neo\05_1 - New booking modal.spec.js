//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Open and check the new booking modal', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Click book from search results'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });
    
    await test.step('Validate all the expected elements are present on the modal for a Meeting Room', async () => { 
        //Header
        await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
        await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
        //Resource name
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Basement Room 2', { exact: true })).toBeVisible();
        //Location
        await expect(page.getByRole('img', { name: 'Location' })).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Basement, Test Building')).toBeVisible();
        //Capacity
        await expect(page.getByRole('img', { name: 'Capacity' })).toBeVisible();
        //Facility
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Adjustable Desk')).toBeVisible();    
        //Booking owner
        await expect(page.getByRole('heading', { name: 'Booking owner' })).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Standard Test Automation User', { exact: true })).toBeVisible();
        //Start time
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Start time')).toBeVisible();
        await expect(page.getByLabel('start-time')).toBeVisible();
        //End time
        await expect(page.getByLabel('New booking', { exact: true }).getByText('End time')).toBeVisible();
        await expect(page.getByLabel('end-time')).toBeVisible();
        //Repeat booking
        await expect(page.getByLabel('Repeat booking')).toBeVisible();
        await expect(page.getByText('Repeat booking')).toBeVisible();
        //Title
        await expect(page.getByText('Title')).toBeVisible();
        await expect(page.getByLabel('Title')).toBeVisible();
        //Notes
        await expect(page.getByText('Notes')).toBeVisible();
        await expect(page.getByLabel('Notes')).toBeVisible();
        //Attendees
        await expect(page.getByRole('heading', { name: 'Attendees' })).toBeVisible();
        await expect(page.getByText('Number of attendees')).toBeVisible();
        await expect(page.getByLabel('Number of attendees')).toBeVisible();
        //Options & layouts
        await expect(page.getByRole('heading', { name: 'Options' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Catering' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Equipment' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Services' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Room layout' })).toBeVisible();

        if (projectViewport.width > 767){
            await expect(page.locator('[id="headlessui-label-\\:r2i\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r2i\\:"]')).toContainText('Date')
            await expect(page.getByLabel('New booking', { exact: true }).getByRole('button', { name: 'Date' })).toBeVisible();
            await expect(page.getByText('Add attendee', { exact: true })).toBeVisible();
            await expect(page.getByPlaceholder('Search by name or email')).toBeVisible();
            await expect(page.getByRole('button', { name: 'Create new attendee Create' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Standard Test Automation User' })).toBeVisible();
            await expect(page.getByLabel('Cancel')).toBeVisible();
        }
        else {
            //Add attendee button specific to small view
            await expect(page.getByRole('button', { name: 'Add attendees Add attendees' })).toBeVisible();
            //Date 
            await expect(page.locator('[id="headlessui-label-\\:r2l\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r2l\\:"]')).toContainText('Date');
            await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/^Date\b.*\bexpandable$/).nth(1)).toBeVisible();
        }
        
        await expect(page.getByRole('button', { name: 'Book', exact: true })).toBeVisible();   
    });

    await test.step('Compare modal against a golden file image', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-bookingModal1.png');
    });

    await test.step('Scroll to the bottom of the modal & compare modal against a golden file image', async () => {
        await page.getByRole('button', { name: 'Room layout' }).scrollIntoViewIfNeeded();
        await expect.soft(page).toHaveScreenshot('Neo-bookingModal2.png');
    });
 
});

test('Close new booking modal early', { tag: '@parallel' }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
       await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
    });
    
    await test.step('Click X and validate the new booking modal closes', async () => { 
        await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
        page.getByRole('button', { name: 'Close panel' }).click();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeHidden();
    });
    
    //The following steps only apply to medium and large views as the Cancel button is not present in small view
    if (projectViewport.width > 767){
        await test.step('Click Book on Basement Room 1 to reopen the new booking modal', async () => {
            await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
            await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
            await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
        });

        await test.step('Click cancel and validate new booking modal closes', async () => {
            await expect(page.getByLabel('Cancel')).toBeVisible();
            await page.getByLabel('Cancel').click();
            await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeHidden();
        });
    }

});

test('Check the capacity element shows when needed', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Capacity'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 9 - Capacity 10', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 9 - Capacity 10' }).click();
    });

    await test.step('Validate the capacity is listed on the modal and the value shown is 10', async () => {
        await expect(page.getByRole('img', { name: 'Capacity' })).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Capacity').first()).toContainText('10');
    });

    await test.step('Compare capacity element against golden file', async () => {
        await expect.soft(page.getByLabel('New booking', { exact: true }).getByLabel('Capacity').first()).toHaveScreenshot('Neo-new-booking-modal-capacityElement.png');
    });

    await test.step('Close the modal, click on Basement Room 1 and validate no capacity is listed', async () => {
        await page.getByRole('button', { name: 'Close panel' }).click();
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
        await expect(page.getByRole('img', { name: 'Capacity' })).toBeHidden();
    });

});

test('Check the facilities show when needed', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Facilities'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 7 - Facilities', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 7 - Facilities' }).click();
    });

    await test.step('Validate the facilities are on the modal as expected', async () => {
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Adjustable Desk')).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Air Conditioning')).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Coffee Machine')).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Computer')).toBeVisible();
    });

    await test.step('Compare the facility pills against the golden file', async () => {
        await expect.soft(page.getByText('Adjustable DeskAir ConditioningCoffee MachineComputer')).toHaveScreenshot('Neo-new-booking-modal-facilities.png');
    });

    await test.step('Close the modal, click on Basement Room 1 and validate no facilities are listed', async () => {
        //Please note: this is actually quite hard to validate as none of the facility pills have any logical IDs so rely on the to be indentified
        await page.getByRole('button', { name: 'Close panel' }).click();
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Adjustable Desk')).toBeHidden();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Air Conditioning')).toBeHidden();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Coffee Machine')).toBeHidden();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel('Computer')).toBeHidden();
    });

});

test('Check the description shows when needed', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Description'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 10 - Description', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 10 - Description' }).click();
    });

    await test.step('Validate the resource description is present on the modal as expected', async () => {
        await expect(page.getByText('This is a description for basement room 10. TestString@#$%^&*()👍😊こんにちは.', { exact:true })).toBeVisible();
    });

    await test.step('Compare the description against the golden file', async () => {
        await expect.soft(page.getByText('This is a description for basement room 10.')).toHaveScreenshot('Neo-new-booking-modal-description.png');
    });

    await test.step('Close the modal, click on Basement Room 1 and validate no description is present', async () => {
        //Please note: this is actually quite hard to validate as the description does not have a logical ID and is identified by the text
        await page.getByRole('button', { name: 'Close panel' }).click();
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
        await expect(page.getByText('This is a description for basement room 1.')).toBeHidden();
    });

});

test('Check the title field', { tag: '@parallel' }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
       await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
    });
    
    await test.step('Check the title field is present', async () => { 
        await expect(page.getByText('Title')).toBeVisible();
        await expect(page.getByLabel('Title')).toBeVisible();
    });

    await test.step('Check the title field is free form text and accepts inputs', async () => { 
        await expect(page.getByLabel('Title')).toBeEnabled();
        await expect(page.getByLabel('Title')).toBeEditable();
        await page.getByLabel('Title').fill('TestString@#$%^&*()👍😊こんにちは');
        await expect(page.getByLabel('Title')).toHaveValue('TestString@#$%^&*()👍😊こんにちは');
    });

});

test('Check the notes field', { tag: '@parallel' }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
       await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
    });
    
    await test.step('Check the title field is present', async () => { 
        await expect(page.getByText('Notes')).toBeVisible();
        await expect(page.getByLabel('Notes')).toBeVisible();
    });

    await test.step('Check the title field is free form text and accepts inputs', async () => { 
        await expect(page.getByLabel('Notes')).toBeEnabled();
        await expect(page.getByLabel('Notes')).toBeEditable();
        await page.getByLabel('Notes').fill('TestString@#$%^&*()👍😊こんにちは');
        await expect(page.getByLabel('Notes')).toHaveValue('TestString@#$%^&*()👍😊こんにちは');
    });

});

test('Mandatory field checks', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Mandatory fields'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 6 - Mandatory fields', async () => {
        await page.getByRole('button', { name: 'Basement Room 6 - Mandatory fields' }).click();
    });

    await test.step('Validate mandatory field messages and asterisks are presented next to mandatory fields', async () => {
        await expect(page.getByText('All fields marked with * are required.')).toBeVisible();
        await expect(page.getByText('Cost code *')).toBeVisible();
        await expect(page.getByText('Title *')).toBeVisible();
        await expect(page.getByText('Notes *')).toBeVisible();
        await expect(page.getByText('Number of attendees *')).toBeVisible();
    });

    await test.step('Click book on the modal with no entries & validate the error messages', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('alert')).toBeVisible();
        await expect(page.getByText('You have 4 errors to correct')).toBeVisible();
        await expect(page.getByRole('link', { name: 'Enter a cost code.' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Enter a title.' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Enter notes.' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Enter the number of attendees.' })).toBeVisible();
    });

    await test.step('Compare error alert panel against golden file', async () => {
        await expect.soft(page.getByRole('alert')).toHaveScreenshot('Neo-new-booking-modal-mandatoryfieldmessage.png');
    });

    await test.step('Validate border is red for cost code & click error', async () => {
        await expect(page.getByLabel('Cost code *')).toHaveCSS('outline-color','rgb(235, 20, 28)');
        await page.getByRole('link', { name: 'Enter a cost code.' }).click();      
        await expect(page.getByLabel('Cost code *')).toBeInViewport();
        await expect(page.getByLabel('Cost code *')).toBeFocused();
        await page.getByRole('alert').scrollIntoViewIfNeeded();
    });
    
    await test.step('Validate border is red for title & click error', async () => {
        await expect(page.getByLabel('Title *')).toHaveCSS('outline-color','rgb(235, 20, 28)');
        await page.getByRole('link', { name: 'Enter a title.' }).click();
        await expect(page.getByLabel('Title *')).toBeInViewport();
        await expect(page.getByLabel('Title *')).toBeFocused();
        await page.getByRole('alert').scrollIntoViewIfNeeded();
    });

    await test.step('Validate border is red for notes & click error', async () => {
        await expect(page.getByLabel('Notes *')).toHaveCSS('outline-color','rgb(235, 20, 28)');
        await page.getByRole('link', { name: 'Enter notes.' }).click();
        await expect(page.getByLabel('Notes *')).toBeInViewport();
        await expect(page.getByLabel('Notes *')).toBeFocused();
        await page.getByRole('alert').scrollIntoViewIfNeeded();
    });

    await test.step('Validate border is red for number of attendees & click error', async () => {
        await expect(page.getByLabel('Number of attendees *')).toHaveCSS('outline-color','rgb(235, 20, 28)');
        await page.getByRole('link', { name: 'Enter the number of attendees.' }).click();
        await expect(page.getByLabel('Number of attendees *')).toBeInViewport();
        await expect(page.getByLabel('Number of attendees *')).toBeFocused();
        await page.getByRole('alert').scrollIntoViewIfNeeded();
    });

});