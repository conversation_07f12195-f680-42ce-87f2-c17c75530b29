import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to End - Edit repeat booking - Weekly', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Edit repeat booking - Weekly' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(email_user_data);
    });

    // Create initial booking for testing edits
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2, 'hour');
    const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
    const bookingTitle = `Weekly Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    const repeatUntilDate = dayjs(bookingFromTime).add(4, 'week');

    // Create a weekly repeat booking for testing edits
    await test.step('Create initial weekly repeat booking', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await searchResultsPage.bookResource('Basement Room 2');
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
        await newBookingModalPage.setupRepeatBooking('Weekly', repeatUntilDate, projectViewport.width);
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.completeBooking();
        await expect.soft(page).toHaveScreenshot('Neo-edit-weekly-initial-booking.png');
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    // Edit details for single occurrence
    const editedSingleTitle = `${bookingTitle} - Single Edit`;
    const editedSingleFromTime = dayjs(bookingFromTime).add(1, 'hour');
    const editedSingleToTime = dayjs(editedSingleFromTime).add(30, 'minutes');
    
    await test.step('Edit single occurrence of weekly repeat booking', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        await expect(newBookingModalPage.modalHeadingEdit).toContainText('Edit booking');
        await expect.soft(page).toHaveScreenshot('Neo-edit-weekly-single-before.png');
        
        await newBookingModalPage.setStartTime(editedSingleFromTime);
        await newBookingModalPage.setEndTime(editedSingleToTime);
        await newBookingModalPage.setTitle(editedSingleTitle);
        await expect.soft(page).toHaveScreenshot('Neo-edit-weekly-single-after.png');
        
        // Keep "Edit just this booking" (default option)
        await newBookingModalPage.saveButton.click();
        await newBookingModalPage.bookingSuccessIcon.waitFor({ state: 'visible' });
        await expect.soft(page).toHaveScreenshot('Neo-edit-weekly-single-success.png');
        
        await newBookingModalPage.goToBookingsList(projectViewport.width);
        
        // Verify single occurrence was updated
        await myBookingsPage.verifyBookingExists(editedSingleFromTime, editedSingleToTime, editedSingleTitle, projectViewport.width);
        
        // Verify next weekly occurrence remains unchanged
        const nextWeekBooking = dayjs(bookingFromTime).add(1, 'week');
        await myBookingsPage.verifyBookingExists(nextWeekBooking, dayjs(nextWeekBooking).add(30, 'minutes'), bookingTitle, projectViewport.width);
    });
    
    // Edit details for all remaining occurrences
    const editedSeriesTitle = `${bookingTitle} - Series Edit`;
    const editedSeriesFromTime = dayjs(bookingFromTime).add(2, 'hour');
    const editedSeriesToTime = dayjs(editedSeriesFromTime).add(30, 'minutes');
    
    await test.step('Edit all remaining occurrences of weekly repeat booking', async () => {
        const nextBookingDate = dayjs(bookingFromTime).add(2, 'week');
        await myBookingsPage.editBooking(nextBookingDate, bookingToTime, bookingTitle, projectViewport.width);
        await expect.soft(page).toHaveScreenshot('Neo-edit-weekly-series-before.png');
        
        await page.getByText('Edit all remaining bookings').click();
        
        // Verify start/end time fields are hidden for series edits
        await expect(newBookingModalPage.startTimeField).toBeHidden();
        await expect(newBookingModalPage.endTimeField).toBeHidden();
        
        // Only change title for series edit
        await newBookingModalPage.setTitle(editedSeriesTitle);
        await expect.soft(page).toHaveScreenshot('Neo-edit-weekly-series-after.png');
        
        await newBookingModalPage.saveButton.click();
        await newBookingModalPage.bookingSuccessIcon.waitFor({ state: 'visible' });
        await expect.soft(page).toHaveScreenshot('Neo-edit-weekly-series-success.png');
        
        await newBookingModalPage.goToBookingsList(projectViewport.width);
        
        // Helper function to get future weekly dates
        const getWeeklyDates = (startDate, endDate) => {
            const dates = [];
            let currentDate = dayjs(startDate);
            while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
                if (currentDate.day() === startDate.day()) {
                    dates.push(currentDate);
                }
                currentDate = currentDate.add(1, 'day');
            }
            return dates;
        };

        // Verify remaining weekly occurrences were updated
        const remainingDates = getWeeklyDates(nextBookingDate, repeatUntilDate);
        for (const date of remainingDates) {
            await myBookingsPage.verifyBookingExists(
                date.hour(editedSeriesFromTime.hour()).minute(editedSeriesFromTime.minute()),
                date.hour(editedSeriesToTime.hour()).minute(editedSeriesToTime.minute()),
                editedSeriesTitle,
                projectViewport.width
            );
        }
    });

    await test.step('Clean up - Cancel all repeat bookings', async () => {
        await myBookingsPage.goto();
        await page.waitForLoadState('networkidle');
        await myBookingsPage.cancelBooking(
            editedSeriesFromTime,
            editedSeriesToTime,
            editedSeriesTitle,
            true,
            projectViewport.width
        );
    });
});
