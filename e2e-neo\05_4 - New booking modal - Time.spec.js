//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Start time - dropdown select - open, check elements and change value', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Start & end time - Select via dropdown'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check start time field is shown and has default value of next 30 min timeslot', async () => {
        let timeNow = dayjs();
        let next30minTimeSlot = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');

        await expect(page.getByLabel('start-time')).toBeVisible();
        await expect(page.getByLabel('start-time')).toHaveValue(`${next30minTimeSlot.format('h:mm A')}`);
    });

    await test.step('Click start time dropdown button & validate options listed', async () => {
        await page.getByLabel('start-time').locator('+ *').filter({ hastext: 'Select a time' }).click();

        const firstStartTime = dayjs().hour(0).minute(0);;
        const lastStartTime = dayjs().hour(23).minute(30);
        let timeSlot = firstStartTime;

        while (timeSlot.isBefore(lastStartTime) || timeSlot.isSame(lastStartTime)) {
        
            await expect(page.getByRole('option', { name: `${timeSlot.format('h:mm A')}`, exact:true})).toBeVisible();
            timeSlot = timeSlot.add(30, 'minute');

        }
        
    });
    
    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-starttime.png');
    });

    await test.step('Select a new value (9:00 PM) & check selection persists', async () => {
        await page.getByRole('option', { name: '9:00 PM', exact: true }).click();
        await expect(page.getByLabel('start-time')).toHaveValue('9:00 PM');
    });

    await test.step('Check end time updated to 1 hour ahead of start time (10:00 PM)', async () => {
        await expect(page.getByLabel('end-time')).toHaveValue('10:00 PM');
    });

});

test('Start time - direct typing - check soft validations and change value', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Start time - Direct typing input'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check start time field is shown and has default value of next 30 min timeslot', async () => {
        let timeNow = dayjs();
        let next30minTimeSlot = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');

        await expect(page.getByLabel('start-time')).toBeVisible();
        await expect(page.getByLabel('start-time')).toHaveValue(`${next30minTimeSlot.format('h:mm A')}`);
    });

    await test.step('Click start time, type 05:06 AM, tab away & confirm value', async () => {
        await page.getByLabel('start-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('start-time').type('05:06 AM');
        await page.keyboard.press('Tab');
        await expect(page.getByLabel('start-time')).toHaveValue('5:06 AM');    
    });

    await test.step('Check end time updated to 1 hour ahead of start time (6:06 AM)', async () => {
        await expect(page.getByLabel('end-time')).toHaveValue('6:06 AM');
    });

    await test.step('Click start time, type 12:01 AM, tab away & confirm value', async () => {
        await page.getByLabel('start-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('start-time').type('12:01 AM');
        await page.keyboard.press('Tab');
        await expect(page.getByLabel('start-time')).toHaveValue('12:01 AM');    
    });

    await test.step('Check end time updated to 1 hour ahead of start time (1:01 AM)', async () => {
        await expect(page.getByLabel('end-time')).toHaveValue('1:01 AM');
    });

    await test.step('Click start time, type 9:3 p, tab away & confirm value', async () => {
        await page.getByLabel('start-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('start-time').type('9:3 p');
        await page.keyboard.press('Tab');
        await expect(page.getByLabel('start-time')).toHaveValue('9:30 PM');    
    });

    await test.step('Check end time updated to 1 hour ahead of start time (10:30 PM)', async () => {
        await expect(page.getByLabel('end-time')).toHaveValue('10:30 PM');
    });

    await test.step('Click start time, type 12:61 AM, tab away & confirm value', async () => {
        await page.getByLabel('start-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('start-time').type('12:61 AM');
        await page.keyboard.press('Tab');
        await expect(page.getByLabel('start-time')).toHaveValue('12:00 AM');    
    });

    await test.step('Check end time updated to 1 hour ahead of start time (1:00 AM)', async () => {
        await expect(page.getByLabel('end-time')).toHaveValue('1:00 AM');
    });

});

test('End time - dropdown select -  open, check elements and change value', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Start & end time - Select via dropdown'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    const timeNow = dayjs();
    const startTime = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');
    const endTime = startTime.add(1, 'hour');

    await test.step('Check end time field is shown and has a default value of 1 hour ahead of start time', async () => {
        await expect(page.getByLabel('end-time')).toBeVisible();
        await expect(page.getByLabel('end-time')).toHaveValue(`${endTime.format('h:mm A')}`);
    });

    await test.step('Click end time dropdown button & validate options listed are from start time +30mins to 11:30pm', async () => {
        await page.getByLabel('end-time').locator('+ *').filter({ hastext: 'Select a time' }).click();

        //Loop to check the 30-minute intervals until 11:30 PM expected for the end time
        const lastEndTime = dayjs().hour(23).minute(30);
        let timeSlot = endTime.subtract(30, 'minute');
        let duration = timeSlot.diff(startTime,'hour', true);

        while (timeSlot.isBefore(lastEndTime) || timeSlot.isSame(lastEndTime)) {

            if(duration===1){

                await expect(page.getByRole('option', { name: `${timeSlot.format('h:mm A')}  (${duration} hr)`, exact:true})).toBeVisible();

            }else{

                await expect(page.getByRole('option', { name: `${timeSlot.format('h:mm A')}  (${duration} hrs)`, exact:true})).toBeVisible(); 
                   
            }

            timeSlot = timeSlot.add(30, 'minute');
            duration = timeSlot.diff(startTime,'hour', true);

        }

    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-endtime.png');
    });

    await test.step('Select a new value (3 hours ahead of current value) & check selection persists', async () => {
        await page.getByRole('option', { name: `${endTime.add(3, 'hour').format('h:mm A')}  (4 hrs)`, exact: true }).click();
        await expect(page.getByLabel('end-time')).toHaveValue(`${endTime.add(3, 'hour').format('h:mm A')}`);
    });

});

test('End time - direct typing - check soft validations and change value', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - End time - Direct typing input'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    const timeNow = dayjs();
    const startTime = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');
    const endTime = startTime.add(1, 'hour');

    await test.step('Check end time field is shown and has default value of next 30 min timeslot', async () => {
        await expect(page.getByLabel('end-time')).toBeVisible();
        await expect(page.getByLabel('end-time')).toHaveValue(`${endTime.format('h:mm A')}`);
    });

    await test.step('Click end time, type 05:06 AM, tab away & confirm value', async () => {
        await page.getByLabel('end-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('end-time').type('05:06 AM');
        await page.keyboard.press('Tab');
        await expect(page.getByRole('alert')).toBeVisible();
        await expect(page.getByRole('alert')).toContainText('Unable to book roomNew bookings may not be in the past.');
        await expect(page.getByLabel('end-time')).toHaveValue('5:06 AM');    
    });

    await test.step('Click end time, type 12:01 AM, tab away & confirm value', async () => {
        await page.getByLabel('end-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('end-time').type('12:01 AM');
        await page.keyboard.press('Tab');
        await expect(page.getByRole('alert')).toBeVisible();
        await expect(page.getByRole('alert')).toContainText('Unable to book roomNew bookings may not be in the past.');
        await expect(page.getByLabel('end-time')).toHaveValue('12:01 AM');    
    });

    await test.step('Click end time, type 9:3 p, tab away & confirm value', async () => {
        await page.getByLabel('end-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('end-time').type('9:3 p');
        await page.keyboard.press('Tab');
        await expect(page.getByLabel('end-time')).toHaveValue('9:30 PM');    
    });

    await test.step('Click end time, type 12:61 AM, tab away & confirm value is is reset to earliest timeslot available', async () => {
        await page.getByLabel('end-time').click();
        await page.keyboard.press('Control+A');
        await page.getByLabel('end-time').type('12:61 AM');
        await page.keyboard.press('Tab');
        await expect(page.getByLabel('end-time')).toHaveValue(`${endTime.subtract(30, 'minute').format('h:mm A')}`);    
    });

});