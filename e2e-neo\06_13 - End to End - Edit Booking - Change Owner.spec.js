//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to end - Edit booking - Change owner - Location Manager user', { tag: '@serial', annotation:{type:'coa', description:'End to end - Edit booking - Change owner'} }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {

    test.slow(); //Sets the timeout to triple the project timeout.

    const locationmanager_user_data = require('../test-data/neo/Users/<USER>');
    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system as Location Manager', async () => {
        await loginPage.simpleLogin(locationmanager_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Change Owner Test - Made by ${locationmanager_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    const bookingNotes = `Change Owner Test Notes - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Create initial booking with Location Manager as owner', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');

        // Book Basement Room 2 using search results page object
        await searchResultsPage.bookResource('Basement Room 2');

        // Set time using page object methods
        await newBookingModalPage.setStartTime(bookingFromTime);

        // Add title and notes using page object methods
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.setNotes(bookingNotes);

        // Verify the current owner is the location manager
        await expect(page.getByText(`Booking owner${locationmanager_user_data.Name}`)).toBeVisible();

        // Complete booking using page object method
        await newBookingModalPage.completeBooking();

        // Navigate to bookings list
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    await test.step('Edit booking - Click Edit booking button', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        await expect(newBookingModalPage.modalHeadingEdit).toContainText('Edit booking');
        
        // Verify current owner is still the location manager
        await expect(page.getByText(`Booking owner${locationmanager_user_data.Name}`)).toBeVisible();
        await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
    });

    await test.step('Change the owner to another user in the organisation', async () => {
        // Click the change owner button
        await newBookingModalPage.changeOwnerButton.click();
        
        // Verify the search field appears and change button is hidden
        await expect(newBookingModalPage.changeOwnerButton).toBeHidden();
        await expect(newBookingModalPage.changeOwnerSearchField).toBeVisible();
        await expect(newBookingModalPage.changeOwnerSearchField).toHaveAttribute('placeholder','Search by name or email address');
        
        // Search for and select the standard user
        await newBookingModalPage.changeOwnerSearchField.fill(standard_user_data.Name);
        await expect(page.getByRole('option', { name: standard_user_data.Name })).toContainText(standard_user_data.Name);
        await expect(page.getByRole('option', { name: standard_user_data.Name })).toContainText(standard_user_data.Email);
        
        // Select the standard user as the new owner
        await page.getByRole('option', { name: standard_user_data.Name }).click();
        
        // Verify the owner has changed
        await expect(page.getByText(`Booking owner${standard_user_data.Name}`)).toBeVisible();
        await expect(page.getByText(standard_user_data.Name, { exact: true })).toBeVisible();
        await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
    });

    await test.step('Save changes and verify modal closes', async () => {
        await newBookingModalPage.saveButton.click();
        await expect(page.getByRole('heading', { name: 'Basement Room 2 booked' })).toBeVisible();
        await expect(newBookingModalPage.bookingSuccessIcon).toBeVisible();
        
        // Navigate back to bookings list
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    await test.step('Verify booking ownership changed by editing the booking again', async () => {
        // Edit the booking again to verify ownership change was saved
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        
        // Verify the owner is now the standard user
        await expect(page.getByText(`Booking owner${standard_user_data.Name}`)).toBeVisible();
        await expect(page.getByText(standard_user_data.Name, { exact: true })).toBeVisible();
        
        // Verify other booking details remain unchanged
        await expect(page.getByLabel('start-time')).toHaveValue(`${bookingFromTime.format('h:mm A')}`);
        await expect(page.getByLabel('end-time')).toHaveValue(`${bookingToTime.format('h:mm A')}`);
        await expect(page.getByLabel('Title')).toHaveValue(bookingTitle);
        await expect(page.getByLabel('Notes')).toHaveValue(bookingNotes);
        
        // Verify the change owner button is still available (location manager still has permissions)
        await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
    });

    await test.step('Clean up - Cancel booking', async () => {
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByLabel('Cancel', { exact: true }).click();
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });
    
});

test('End to end - Edit booking - Change owner - Admin user', { tag: '@serial', annotation:{type:'coa', description:'End to end - Edit booking - Change owner'} }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    
    test.slow(); //Sets the timeout to triple the project timeout.

    const admin_user_data = require('../test-data/neo/Users/<USER>');
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system as Admin', async () => {
        await loginPage.simpleLogin(admin_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(3,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Admin Change Owner Test - Made by ${admin_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Create initial booking with Admin as owner', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');

        // Book Basement Room 1 using search results page object
        await searchResultsPage.bookResource('Basement Room 1');

        // Set time using page object method
        await newBookingModalPage.setStartTime(bookingFromTime);

        // Add title using page object method
        await newBookingModalPage.setTitle(bookingTitle);

        // Complete booking using page object method
        await newBookingModalPage.completeBooking();

        // Navigate to bookings list
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    await test.step('Edit booking and change owner to Email user', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        
        // Change owner to email user
        await newBookingModalPage.changeOwnerButton.click();
        await newBookingModalPage.changeOwnerSearchField.fill(email_user_data.Name);
        await page.getByRole('option', { name: email_user_data.Name }).click();
        
        // Save changes
        await newBookingModalPage.saveButton.click();
        await expect(page.getByRole('heading', { name: 'Basement Room 1 booked' })).toBeVisible();
        
        // Navigate back to bookings list
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    await test.step('Verify ownership change was successful', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        
        // Verify the owner is now the email user
        await expect(page.getByText(`Booking owner${email_user_data.Name}`)).toBeVisible();
    });

    await test.step('Clean up - Cancel booking', async () => {
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByLabel('Cancel', { exact: true }).click();
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });
    
});
