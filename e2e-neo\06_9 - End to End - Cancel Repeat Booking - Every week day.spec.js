import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to End - Cancel repeat booking - Every week day', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Cancel repeat booking - Every week day' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login and create a repeat booking to cancel', async () => {
        await loginPage.simpleLogin(email_user_data);

        // Create initial repeat booking for testing cancellation
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const repeatUntilDate = dayjs(bookingFromTime).add(2, 'week');
        const bookingTitle = `Cancel Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm')}`;

        // Create repeat booking
        await page.getByRole('button', { name: 'Search' }).click();
        await searchResultsPage.bookResource('Basement Room 2');
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
        await newBookingModalPage.setupRepeatBooking('Every week day', repeatUntilDate, projectViewport.width);
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.completeBooking();
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    await test.step('Verify cancel dialog options for repeat booking', async () => {
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Cancel Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm')}`;

        // Open cancel dialog
        await myBookingsPage.openCancelDialog(bookingFromTime, bookingTitle, projectViewport.width);

        // Verify dialog elements and default selection states
        await expect(myBookingsPage.cancelDialogTitle).toBeVisible();
        await expect(myBookingsPage.cancelDialogMessage).toHaveText('This action cannot be undone and will make the room available for others to book.');
        await expect(myBookingsPage.cancelSingleButton).toHaveText('Cancel just this booking');
        await expect(myBookingsPage.cancelAllButton).toHaveText('Cancel all remaining bookings in this series');
        await expect(myBookingsPage.cancelDialogCloseButton).toBeVisible();
        await expect(myBookingsPage.confirmCancelButton).toBeVisible();

        // Verify radio button states
        await expect(myBookingsPage.cancelSingleButton).toHaveClass(/cursor-not-allowed/);
        await expect(myBookingsPage.cancelSingleButton).toHaveClass(/bg-border-decoration/);
        await expect(myBookingsPage.cancelSingleButton).toBeDisabled();
        
        await expect(myBookingsPage.cancelAllButton).not.toHaveClass(/cursor-not-allowed/);
        await expect(myBookingsPage.cancelAllButton).not.toBeDisabled();

        await expect.soft(page).toHaveScreenshot('Neo-cancel-repeat-dialog.png');

        // Test close button
        await myBookingsPage.cancelDialogCloseButton.click();
        if (projectViewport.width < 768) {
            await page.getByRole('button', { name: 'close' }).click();
        }
        await myBookingsPage.verifyBookingExists(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
    });

    await test.step('Cancel single occurrence of repeat booking', async () => {
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Cancel Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
        const nextDayBooking = dayjs(bookingFromTime).add(1, 'day');

        // Cancel single occurrence
        await myBookingsPage.cancelBooking(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            false, // cancelAllInstances = false
            projectViewport.width
        );

        // Verify next occurrence still exists
        await myBookingsPage.verifyBookingExists(
            nextDayBooking,
            dayjs(nextDayBooking).add(30, 'minutes'),
            bookingTitle,
            projectViewport.width
        );

        await expect.soft(page).toHaveScreenshot('Neo-cancel-repeat-single.png');
    });

    await test.step('Cancel all remaining occurrences', async () => {
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Cancel Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
        const nextDayBooking = dayjs(bookingFromTime).add(1, 'day');

        // Cancel all remaining occurrences
        await myBookingsPage.cancelBooking(
            nextDayBooking,
            bookingToTime,
            bookingTitle,
            true, // cancelAllInstances = true
            projectViewport.width
        );

        // Verify first occurrence was removed
        await myBookingsPage.verifyBookingDoesNotExist(
            nextDayBooking,
            bookingTitle,
            projectViewport.width
        );

        // Verify a future occurrence is also removed
        const futureDayBooking = dayjs(bookingFromTime).add(5, 'day');
        await myBookingsPage.verifyBookingDoesNotExist(
            futureDayBooking,
            bookingTitle,
            projectViewport.width
        );

        await expect.soft(page).toHaveScreenshot('Neo-cancel-repeat-all.png');
    });
});
