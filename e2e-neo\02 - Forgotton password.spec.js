//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check forgotten password page', { tag: '@parallel' }, async ({ page, loginPage }) => {

    await test.step('Navigate to the Neo login page & click forgotten password', async () => {
        await loginPage.goto();
        await expect(page.getByRole('link', { name: 'Forgot your password?' })).toBeVisible();
        await loginPage.forgottenPasswordLink.click();
    });

    await test.step('Validate expected UI elements are presented', async () => {
        await expect(page.locator('img')).toBeVisible();
        await expect(page).toHaveURL('/neo/forgot-password');
        await expect(page.getByRole('heading', { name: 'Forgot your password?' })).toBeVisible();
        await expect(page.getByRole('paragraph')).toContainText('Enter your email address and we will send details on how to reset your password.');
        await expect(page.getByLabel('Email')).toBeVisible();
        await expect(page.getByRole('button', { name: 'Send reset details' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Back to login' })).toBeVisible();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-forgot-password-Page.png');
    });

});

test('Attempt to use forgotten password with invalid details', { tag: '@parallel' }, async ({ page, loginPage }) => {

    await test.step('Navigate to the Neo login page & click forgotten password', async () => {
        await loginPage.goto();
        await loginPage.forgottenPasswordLink.click();
    });

    await test.step('Attempt to use forgotten password with blank email address', async () => {
        await page.getByRole('button', { name: 'Send reset details' }).click();
        await expect(page.getByRole('heading', { name: 'Unable to send reset email' })).toBeVisible();
        await expect(page.getByText('Please check your email')).toBeVisible();
    });

    await test.step('Attempt to use forgotten password with invalid email address', async () => {
        await page.getByLabel('Email').fill('notarealemail');
        await page.getByRole('button', { name: 'Send reset details' }).click();
        await expect(page.getByRole('heading', { name: 'Unable to send reset email' })).toBeVisible();
        await expect(page.getByText('Please check your email')).toBeVisible();
    });

    await test.step('Attempt to use forgotten password with fake users email address', async () => {
        await page.getByLabel('Email').fill('<EMAIL>');
        await page.getByRole('button', { name: 'Send reset details' }).click();
        await expect(page).toHaveURL('/neo/reset-password');
        await expect(page.getByRole('heading', { name: 'Check your email' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Back to login' })).toBeVisible();
    });

});

test('Use forgotten password as Standard user', { tag: '@parallel' }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Navigate to the Neo login page & click forgotten password', async () => {
        await loginPage.goto();
        await loginPage.forgottenPasswordLink.click();
    });

    await test.step('Attempt to use forgotten password with Standard users email address', async () => {
        await page.getByLabel('Email').fill(standard_user_data.Email);
        await page.getByRole('button', { name: 'Send reset details' }).click();
        await expect(page).toHaveURL('/neo/reset-password');
        await expect(page.getByRole('heading', { name: 'Check your email' })).toBeVisible();
        await expect(page.getByRole('link', { name: 'Back to login' })).toBeVisible();
    });

    await test.step('Click back to login', async () => {
        await page.getByRole('link', { name: 'Back to login' }).click();
        await expect(page).toHaveURL('/neo/login');
        await expect(page.getByLabel('Email')).toBeVisible();
    });

});

test('Check reset password page', { tag: '@parallel' }, async ({ page, loginPage }) => {

    await test.step('Navigate to the Neo login page & click forgotten password', async () => {
        await loginPage.goto();
        await loginPage.forgottenPasswordLink.click();
    });

    await test.step('Attempt to use forgotten password with fake users email address', async () => {
        await page.getByLabel('Email').fill('<EMAIL>');
        await page.getByRole('button', { name: 'Send reset details' }).click();
        await expect(page).toHaveURL('/neo/reset-password');
    });

    await test.step('Validate UI elements are present', async () => {
        await expect(page.locator('img')).toBeVisible();
        await expect(page.getByRole('heading', { name: 'Check your email' })).toBeVisible();
        await expect(page.getByText('We have sent you a link that')).toBeVisible();
        await expect(page.getByRole('link', { name: 'Back to login' })).toBeVisible();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-reset-password-Page.png');
    });

});