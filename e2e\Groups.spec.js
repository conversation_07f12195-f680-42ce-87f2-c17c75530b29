//import test and expect functionality
import { test, expect } from '@playwright/test';
import exp from 'constants';
import dayjs from 'dayjs';

//Set name of test for video
const testName = 'Groups';

//Set video recording path
const recordingFilePath = `Videos/Webapp/${testName}/${testName} ${dayjs().format('DD-MM-YYYY HH꞉mm꞉ss')}.webm`;

// Test constants
const domainGroupName = `Test Domain Group ${dayjs().format('YYYYMMDDHHmmss')}`;
const advancedGroupName = `Test Advanced Group ${dayjs().format('YYYYMMDDHHmmss')}`;

test(testName, async ({ browser, baseURL }, testInfo) => {

  let context = await browser.newContext({
    recordVideo: {
      dir: testInfo.outputPath('videos'),
      size: { width: 1500, height: 800 },
    },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36'
  });
  let page = await context.newPage();


  //Load data for the standard user
  const org = require(`../test-data/e2e/Org${testInfo.parallelIndex}_data.json`);
  const data = org.users.find(user => user.role === 'admin');
  await page.goto('ui/#/login');
  await page.getByLabel('Email').fill(data.Email);
  await page.getByLabel('Password').fill(data.Password);
  await page.getByRole('button', { name: 'Log in' }).click();

  // GIVEN: I am on the Admin page
  await page.getByRole('link', { name: 'Admin', exact: true }).click();
  await expect(page.getByText('Account Settings')).toBeVisible();

  // WHEN: I choose the Groups option
  await page.getByRole('link', { name: 'Groups' }).click();

  // THEN: I am presented with the Groups sections
  await expect(page.getByRole('heading', { name: 'User groups' })).toBeVisible();
  await expect(page.getByRole('heading', { name: 'Domain groups' })).toBeVisible();
  await expect(page.getByRole('heading', { name: 'Advanced groups' })).toBeVisible();

  // GIVEN: I am on the Groups page in the admin section of the Matrix Booking app
  // WHEN: I select 'Add user group'
  await page.getByRole('button', { name: 'Add user group' }).click();

  // THEN: The 'Add user group' modal is presented with the following
  await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Add user group' })).toBeVisible();
  await expect(page.getByRole('heading', { level: 4 }).filter({ hasText: 'Group name' })).toBeVisible();
  await expect(page.getByRole('heading', { level: 4 }).filter({ hasText: 'Is this an organisational group used for reporting?' })).toBeVisible();
  await expect(page.locator('.org-unit-switch')).toBeVisible();
  await expect(page.getByText('No', { exact: true })).toBeVisible();
  await expect(page.locator('label').filter({ hasText: 'Users (0)' }).getByRole('paragraph')).toBeVisible();
  await expect(page.getByRole('button', { name: 'Save' })).toBeVisible();
  await expect(page.getByText('Close')).toBeVisible();

  // AND: The reporting toggle switch can be toggled
  await page.locator('.org-unit-switch').click();
  await expect(page.getByText('Yes')).toBeVisible();

  // AND: Selecting Close closes the modal
  await page.getByText('Close').click();
  await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Add user group' })).not.toBeVisible();

  // GIVEN: I have opened the 'Add user group' modal
  await page.getByRole('button', { name: 'Add user group' }).click();

  // WHEN: I add a group name to the 'Group name' field
  await page.getByRole('textbox').first().fill('Test Group');

  // AND: Select the Save button
  await page.getByRole('button', { name: 'Save' }).click();

  // THEN: The modal is dismissed
  await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Add user group' })).not.toBeVisible();

  // AND: The new group is listed in the user group section (with no users)
  await expect(page.getByRole('row', { name: /Test Group/ })).toBeVisible();
  await expect(page.getByRole('cell', { name: '0', exact: true })).toBeVisible();
  await expect(page.getByRole('cell', { name: '– Not used –' })).toBeVisible();

  // GIVEN: I have opened the 'Add user group' modal
  await page.getByRole('button', { name: 'Add user group' }).click();
  await page.getByRole('textbox').first().fill('Test Group with Users');

  // WHEN: I type a user's name in the users field
  await page.locator('tags-input').getByPlaceholder('Type to search for someone').click()
  await page.locator('tags-input').getByPlaceholder('Type to search for someone').fill('test');

  // THEN: After the third character typed the type ahead lookup lists users matching the search
  await page.waitForTimeout(500); // Give the UI time to update

  // AND: A users name can be chosen - use a more specific selector
  await page.locator('li').filter({ hasText: data.Email }).first().click();
  // AND: Once chosen is listed in the 'Users' field
  // AND: Once populated the indicating number increments by 1
  await expect(page.locator('label').filter({ hasText: 'Users (1)' }).getByRole('paragraph')).toBeVisible();

  // WHEN: I select the 'Save' button
  await page.getByRole('button', { name: 'Save' }).click();

  // THEN: The modal is dismissed
  await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Add user group' })).not.toBeVisible();
  // AND: The new group is listed in the user group section (with the previously user/s populated)
  await expect(page.getByRole('row', { name: /Test Group with Users/ })).toBeVisible();
  await expect(page.getByRole('cell', { name: '1' })).toBeVisible();
  await expect(page.getByRole('row', { name: /Test Group with Users/ }).getByRole('cell', { name: '– Not used –' })).toBeVisible();

  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already User groups configured
  // WHEN: I check the available options
  // THEN: Each group has an 'Edit' and 'Delete' button available
  await test.step('Verify group action buttons', async () => {
    const testGroup = page.getByRole('row', { name: /Test Group with Users/ });
    await expect(testGroup.getByRole('button', { name: 'Edit' })).toBeVisible();
    await expect(testGroup.getByRole('button', { name: 'Delete' })).toBeVisible();
  });

  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already User groups configured
  // WHEN: I select the 'Edit' option
  // THEN: The 'Edit user group' modal is presented
  await test.step('Check edit group modal', async () => {
    await page.getByRole('row', { name: /Test Group with Users/ }).getByRole('button', { name: 'Edit' }).click();
    await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Edit user group' })).toBeVisible();
    await expect(page.getByRole('heading', { level: 4 }).filter({ hasText: 'Group name' })).toBeVisible();
    await expect(page.getByRole('heading', { level: 4 }).filter({ hasText: 'Is this an organisational group used for reporting?' })).toBeVisible();
  });

  // GIVEN: The 'Edit user group' modal is displayed
  // WHEN: I click the 'Close' button
  // THEN: The modal is dismissed
  // AND: No changes to the group have been made
  await test.step('Check edit modal close', async () => {
    await page.getByText('Close').click();
    const userCountBefore = await page.getByRole('row', { name: /Test Group with Users/ }).getByRole('cell', { name: '1' }).textContent();
    await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Edit user group' })).not.toBeVisible();
    const userCountAfter = await page.getByRole('row', { name: /Test Group with Users/ }).getByRole('cell', { name: '1' }).textContent();
    expect(userCountAfter).toBe(userCountBefore);
  });

  // GIVEN: The 'Edit user group' modal is displayed
  // WHEN: I remove a user from the 'Users' section
  // AND: Select the 'Save' button
  // THEN: The modal is dismissed
  // AND: The changes are saved
  await test.step('Remove user from group', async () => {
    await page.getByRole('row', { name: /Test Group with Users/ }).getByRole('button', { name: 'Edit' }).click();
    await page.locator('tags-input').getByRole('listitem').getByText('×').click();
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Edit user group' })).not.toBeVisible();
    await expect(page.getByRole('row', { name: /Test Group with Users/ }).getByRole('cell', { name: '0' })).toBeVisible();
  });
  // GIVEN: The 'Edit user group' modal is displayed
  // WHEN: I add a user to the 'Users' section
  // AND: Select the 'Save' button
  // THEN: The modal is dismissed
  // AND: The changes are saved
  await test.step('Add user to group', async () => {
    await page.getByRole('row', { name: /Test Group with Users/ }).getByRole('button', { name: 'Edit' }).click();
    await page.locator('tags-input').getByPlaceholder('Type to search for someone').click();
    await page.locator('tags-input').getByPlaceholder('Type to search for someone').fill('test');

    // Wait for the suggestion list to appear
    await page.waitForTimeout(500); // Give the UI time to update

    // Select the user from the suggestion list - use a more specific selector
    await page.locator('li').filter({ hasText: data.Email }).first().click();
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Edit user group' })).not.toBeVisible();
    await expect(page.getByRole('row', { name: /Test Group with Users/ }).getByRole('cell', { name: '1' })).toBeVisible();
  });
  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already User groups configured
  // WHEN: I select the 'Delete' option
  // THEN: The user group is removed from the list
  await test.step('Delete user group', async () => {
    // Verify group exists before deletion
    await expect(page.getByRole('cell', { name: 'Test Group', exact: true })).toBeVisible();

    // Click delete button and verify group is removed
    await page.getByRole('cell', { name: 'Test Group', exact: true }).locator('..').getByRole('button', { name: 'Delete' }).click();
    await expect(page.getByRole('cell', { name: 'Test Group', exact: true })).not.toBeVisible();
  });
  // GIVEN: I am on the Groups page in the admin section of the Matrix Booking app
  // WHEN: I select 'Add domain group'
  // THEN: The 'Add Domain group' model is presented with the following
  // - A Name field
  // - An add domain field
  // - A 'Save' button
  // - A 'Close' button
  // AND: Selecting 'Close' dismisses the modal
  await test.step('Check domain group modal elements', async () => {
    await page.getByRole('button', { name: 'Add domain group' }).click();
    await expect(page.getByRole('heading', { level: 1, name: 'Add  Domain Group' })).toBeVisible();
    await expect(page.getByPlaceholder('Name')).toBeVisible();
    await expect(page.locator('tags-input input[placeholder="Add Domain"]')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Save' })).toBeVisible();
    await expect(page.getByText('Close')).toBeVisible();

    // Check close functionality
    await page.getByText('Close').click();
    await expect(page.getByRole('heading', { level: 1, name: 'Add  Domain Group' })).not.toBeVisible();
  });

  // GIVEN: The 'Add Domain group' modal has been opened
  // WHEN: A name has been typed in the 'Name' field
  // AND: Select the 'Save' button
  // THEN: The modal is dismissed
  // AND: The new group is listed in the 'Domain groups' section
  // AND: Has no domains listed
  await test.step('Create domain group without domains', async () => {
    await page.getByRole('button', { name: 'Add domain group' }).click();
    await page.getByPlaceholder('Name').fill('Test Domain Group');
    await page.getByRole('button', { name: 'Save' }).click();

    // Verify group was created
    await expect(page.getByRole('cell', { name: 'Test Domain Group' })).toBeVisible();
    await expect(page.getByRole('cell', { name: 'No domains have been assigned' })).toBeVisible();
  });

  // GIVEN: The 'Add Domain group' modal has been opened
  // AND: A name has been entered in the 'Name' field
  // WHEN: I enter one or more domain into the domain field
  // THEN: They are each listed in the 'Add domain' field
  // AND: Each domain entered has an X to allow the user to remove the individual domain
  await test.step('Check domain input functionality', async () => {
    await page.getByRole('button', { name: 'Add domain group' }).click();
    await page.getByRole('textbox', { name: 'Name' }).fill('Test Domain Group With Domains');
    await page.locator('tags-input').getByPlaceholder('Add Domain').fill('test1.com');
    await page.keyboard.press('Enter');
    await expect(page.getByText('test1.com ×')).toBeVisible();
    await page.locator('tags-input').getByPlaceholder('Add Domain').fill('test2.com');
    await page.keyboard.press('Enter');
    await expect(page.getByText('test2.com ×')).toBeVisible();


    // Verify domains are listed and have remove buttons
    await expect(page.locator('tags-input').getByRole('listitem')).toHaveCount(2);
    await expect(page.locator('tags-input').getByRole('listitem').first().getByText('×')).toBeVisible();
    await expect(page.locator('tags-input').getByRole('listitem').nth(1).getByText('×')).toBeVisible();

    // Test remove functionality for verification
    await page.locator('tags-input').getByRole('listitem').first().getByText('×').click();
    await expect(page.locator('tags-input').getByRole('listitem')).toHaveCount(1);

    // Add the domain back
    await page.locator('tags-input').getByPlaceholder('Add Domain').fill('test1.com');
    await page.keyboard.press('Enter');
  });

  // GIVEN: The 'Add Domain group' modal has been opened
  // AND: A name has been entered in the 'Name' field
  // AND: One or more domains have been entered into the domain field
  // WHEN: I Select the 'Save' button
  // THEN: The modal is dismissed
  // AND: The new domain group is listed in the 'Domain groups' section
  // AND: The name and domains are correctly listed
  await test.step('Save domain group with domains', async () => {
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('heading', { level: 1 }).filter({ hasText: 'Add domain group' })).not.toBeVisible();
    const domainGroupRow = page.getByRole('row', { name: /Test Domain Group With Domains/ });
    await expect(domainGroupRow).toBeVisible();
    await expect(domainGroupRow.getByText('test1.com')).toBeVisible();
    await expect(domainGroupRow.getByText('test2.com')).toBeVisible();
  });

  // GIVEN: I am on the Groups page in the admin section of the Matrix Booking app
  // WHEN: I select 'Add domain group' and enter invalid domains
  // THEN: Error messages are displayed appropriately
  await test.step('Test domain input validation', async () => {
    await page.getByRole('button', { name: 'Add domain group' }).click();

    // Try to save without a name
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByText('Please fill all fields.')).toBeVisible();

    // Test invalid domain format
    await page.getByPlaceholder('Name').fill('Test Invalid Domain Group');
    const domainInput = page.locator('tags-input input[placeholder="Add Domain"]');

    // Test valid domain format clears error
    await domainInput.fill('valid-domain.com');
    await page.keyboard.press('Enter');
    await page.getByRole('button', { name: 'Save' }).click();

    await expect(page.getByRole('cell', { name: 'Test Invalid Domain Group' })).toBeVisible();
    await expect(page.getByRole('cell', { name: 'valid-domain.com' })).toBeVisible();

  });

  // GIVEN: I have an existing domain group
  // WHEN: I edit the group
  // THEN: The changes are saved correctly
  await test.step('Edit existing domain group', async () => {
    // First create a domain group to edit
    await page.getByRole('button', { name: 'Add domain group' }).click();
    await page.getByPlaceholder('Name').fill(domainGroupName);
    const domainInput = page.locator('tags-input input[placeholder="Add Domain"]');
    await domainInput.fill('initial-domain.com');
    await page.keyboard.press('Enter');
    await page.getByRole('button', { name: 'Save' }).click();

    // Edit the group
    await page.getByRole('row').filter({ has: page.getByText(domainGroupName) })
      .getByRole('button', { name: 'Edit' }).click();

    // Update name and domains
    const editedName = `${domainGroupName} - Edited`;
    await page.getByRole('region').getByText('Test Domain Group').click()
    await page.getByPlaceholder('Name').fill(editedName);
    await domainInput.fill('new-domain.com');
    await page.keyboard.press('Enter');

    // Remove old domain
    await page.locator('tags-input .tag-item').filter({ hasText: 'initial-domain.com' })
      .locator('.remove-button').click();

    await page.getByRole('button', { name: 'Save' }).click();

    // Verify changes
    await expect(page.getByRole('cell', { name: editedName })).toBeVisible();
    await expect(page.getByRole('cell', { name: 'new-domain.com' })).toBeVisible();
    await expect(page.getByRole('cell', { name: 'initial-domain.com' })).not.toBeVisible();
  });

  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already Domain groups configured
  // WHEN: I check the available options
  // THEN: Each group has an 'Edit' and 'Delete' button available
  await test.step('Verify domain group action buttons', async () => {
    const domainGroup = page.getByRole('row', { name: /Test Domain Group With Domains/ });
    await expect(domainGroup.getByRole('button', { name: 'Edit' })).toBeVisible();
    await expect(domainGroup.getByRole('button', { name: 'Delete' })).toBeVisible();
  });

  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already Domain groups configured
  // WHEN: I select the 'Edit' option
  // THEN: The 'Edit Domain group' modal is presented
  await test.step('Check edit domain group modal', async () => {
    await page.getByRole('row', { name: /Test Domain Group With Domains/ })
      .getByRole('button', { name: 'Edit' }).click();
    await expect(page.getByRole('heading', { level: 1, name: 'Edit  Domain Group' })).toBeVisible();
    await expect(page.getByRole('region').getByText('Test Domain Group With Domains')).toBeVisible();
    await expect(page.locator('input[type="text"]')).toBeVisible();
  });

  // GIVEN: The 'Edit Domain group' modal is displayed
  // WHEN: I click the 'Close' button
  // THEN: The modal is dismissed
  // AND: No changes to the group have been made
  await test.step('Check edit domain modal close', async () => {
    // Store initial domains for comparison
    const initialDomains = await page.locator('tags-input .tag-item').count();
    await page.getByText('Close').click();
    await expect(page.getByRole('heading', { level: 1, name: 'Edit  Domain Group' })).not.toBeVisible();

    // Reopen modal to verify no changes were made
    await page.getByRole('row', { name: /Test Domain Group With Domains/ })
      .getByRole('button', { name: 'Edit' }).click();
    const domainsAfterClose = await page.locator('tags-input .tag-item').count();
    expect(domainsAfterClose).toBe(initialDomains);
    await page.getByText('Close').click();
  });

  // GIVEN: The 'Edit Domain group' modal is displayed
  // WHEN: I remove a domain from listed domains
  // AND: Select the 'Save' button
  // THEN: The modal is dismissed
  // AND: The changes are saved
  await test.step('Remove domain from group', async () => {
    await page.getByRole('row', { name: /Test Domain Group With Domains/ })
      .getByRole('button', { name: 'Edit' }).click();

    // Remove one domain
    await page.locator('tags-input .tag-item').filter({ hasText: 'test2.com' })
      .getByText('×').click();
    await page.getByRole('button', { name: 'Save' }).click();

    // Verify changes
    await expect(page.getByRole('heading', { level: 1, name: 'Edit  Domain Group' })).not.toBeVisible();
    await expect(page.getByRole('cell', { name: 'test2.com' })).not.toBeVisible();
    await expect(page.getByRole('cell', { name: 'test1.com' })).toBeVisible();
  });

  // GIVEN: The 'Edit Domain group' modal is displayed
  // WHEN: I add a domain to the listed domains
  // AND: Select the 'Save' button
  // THEN: The modal is dismissed
  // AND: The changes are saved
  await test.step('Add domain to group', async () => {
    await page.getByRole('row', { name: /Test Domain Group With Domains/ })
      .getByRole('button', { name: 'Edit' }).click();

    // Add new domain
    const domainInput = page.locator('tags-input input[placeholder="Add Domain"]');
    await domainInput.fill('test3.com');
    await page.keyboard.press('Enter');
    await page.getByRole('button', { name: 'Save' }).click();

    // Verify changes
    await expect(page.getByRole('heading', { level: 1, name: 'Edit  Domain Group' })).not.toBeVisible();
    await expect(page.getByRole('cell', { name: 'test3.com' })).toBeVisible();
    await expect(page.getByRole('cell', { name: 'test1.com' })).toBeVisible();
  });

  // GIVEN: I am on the Groups page in the admin section of the Matrix Booking app
  // WHEN: I select the 'Add advanced group' button
  // THEN: The 'Add advanced group' modal is presented with the expected elements
  await test.step('Check advanced group modal elements', async () => {
    await page.getByRole('button', { name: 'Add advanced group' }).click();

    // Verify modal title and required fields indicator
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).toBeVisible();
    await expect(page.getByText('* Required Information')).toBeVisible();

    // Verify Name field with required indicator
    await expect(page.getByLabel('Name *')).toBeVisible();

    // Verify User group rules section
    await expect(page.getByText('User group rules')).toBeVisible();
    await expect(page.getByText('Do not use user groups')).toBeVisible();
    // Check for the text content of the radio options
    await expect(page.getByText('Include users in')).toBeVisible();
    await expect(page.getByText('Exclude users in')).toBeVisible();

    // Verify Domain group rules section
    await expect(page.getByText('Domain group rules')).toBeVisible();
    await expect(page.getByText('Do not use domain groups')).toBeVisible();
    // Check for the text content of the radio options
    await expect(page.getByText('Include users whose email matches')).toBeVisible();
    await expect(page.getByText('Exclude users whose email matches')).toBeVisible();

    // Verify Save and Close buttons
    await expect(page.getByRole('button', { name: 'Save' })).toBeVisible();
    await expect(page.getByText('Close')).toBeVisible();

    // Verify Close button dismisses the modal
    await page.getByText('Close').click();
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).not.toBeVisible();
  });

  // GIVEN: the 'Add advanced group' modal is displayed
  // WHEN: A name is entered in the 'Name' field
  // AND: The 'Save' button is selected
  // THEN: The modal is closed
  // AND: The new group is listed under the 'Advance groups' section
  await test.step('Create advanced group with name only', async () => {
    // Open modal and enter name
    await page.getByRole('button', { name: 'Add advanced group' }).click();
    await page.getByLabel('Name *').fill(advancedGroupName);

    // Save and verify modal closes
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).not.toBeVisible();

    // Verify new group appears in list - use a more specific selector
    await expect(page.getByRole('row').filter({ hasText: advancedGroupName }).first()).toBeVisible();
  });

  // GIVEN: the 'Add advanced group' modal is displayed
  // AND: The Name field has been populated
  // WHEN: Either of the Include or Exclude radio buttons are selected in the 'User group rules' section is chosen
  // THEN: A 'User groups' field is displayed (with an indicator advising this is a required field)
  // AND: User groups can be searched and selected
  // AND: Selecting 'Save' button dismisses the modal and the new group is listed
  await test.step('Create advanced group with user group rules', async () => {
    const userGroupAdvancedName = `User Group Rules ${dayjs().format('YYYYMMDDHHmmss')}`;

    // Open modal and enter name
    await page.getByRole('button', { name: 'Add advanced group' }).click();
    await page.getByLabel('Name *').fill(userGroupAdvancedName);

    // Select Include users option
    await page.getByText('Include users in').click();

    // Verify User groups field appears with required indicator
    await expect(page.getByLabel('User groups *')).toBeVisible();

    // Search and select a user group
    await page.locator('#userGroupTag').click();
    await page.locator('#userGroupTag').fill('Test');

    // Wait for the suggestion list to appear and then select the group
    // Use a more specific approach to handle multiple suggestion lists
    await page.waitForTimeout(500); // Give the UI time to update

    // Click on the first suggestion that contains the text we're looking for
    await page.locator('li').filter({ hasText: /Test Group/ }).first().click();

    // Save and verify modal closes
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).not.toBeVisible();

    // Verify new group appears in list - use a more specific selector
    await expect(page.getByRole('row').filter({ hasText: userGroupAdvancedName }).first()).toBeVisible();
  });

  // GIVEN: the 'Add advanced group' modal is displayed
  // AND: The Name field has been populated
  // WHEN: Either of the Include or Exclude radio buttons are selected in the 'Domain group rules' section is chosen
  // THEN: A 'Domain groups' field is displayed (with an indicator advising this is a required field)
  // AND: Domain groups can be searched and selected
  // AND: Selecting 'Save' button dismisses the modal and the new group is listed
  await test.step('Create advanced group with domain group rules', async () => {
    const domainGroupAdvancedName = `Domain Group Rules ${dayjs().format('YYYYMMDDHHmmss')}`;

    // Open modal and enter name
    await page.getByRole('button', { name: 'Add advanced group' }).click();
    await page.getByLabel('Name *').fill(domainGroupAdvancedName);

    // Select Include users option for domain groups
    await page.getByText('Include users whose email matches').click();

    // Verify Domain groups field appears with required indicator
    await expect(page.getByLabel('Domain groups *')).toBeVisible();

    // Search and select a domain group
    await page.locator('#domainGroupTag').click();
    await page.locator('#domainGroupTag').fill('Test');

    // Wait for the suggestion list to appear and then select the domain group
    // Use a more specific approach to handle multiple suggestion lists
    await page.waitForTimeout(500); // Give the UI time to update

    // Click on the first suggestion that contains the text we're looking for
    await page.locator('li').filter({ hasText: /Test Domain Group/ }).first().click();

    // Save and verify modal closes
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).not.toBeVisible();

    // Verify new group appears in list - use a more specific selector
    await expect(page.getByRole('row').filter({ hasText: domainGroupAdvancedName }).first()).toBeVisible();
  });

  // GIVEN: the 'Add advanced group' modal is displayed
  // AND: The Name field has been populated
  // AND: User group rules and at least one group has been added
  // AND: Domain group rules and at least one group has been added
  // WHEN: I select the Save button
  // THEN: The modal is dismissed
  // AND: The new group is listed
  await test.step('Create advanced group with both user and domain group rules', async () => {
    const combinedRulesName = `Combined Rules ${dayjs().format('YYYYMMDDHHmmss')}`;

    // Open modal and enter name
    await page.getByRole('button', { name: 'Add advanced group' }).click();
    await page.getByLabel('Name *').fill(combinedRulesName);

    // Configure user group rules
    await page.locator('#sgUserRule2').click();
    await page.locator('#userGroupTag').click();
    await page.locator('#userGroupTag').fill('Test');

    // Wait for the suggestion list to appear and then select the group
    // Use a more specific approach to handle multiple suggestion lists
    await page.waitForTimeout(500); // Give the UI time to update

    // Click on the first suggestion that contains the text we're looking for
    await page.locator('li').filter({ hasText: /Test Group/ }).first().click();

    // Configure domain group rules
    await page.locator('#sgDomainRule2').click();

    // Verify note about precedence appears
    await expect(page.getByText('Note: User group rules take precedence over domain rules.')).toBeVisible();

    // Select domain group
    await page.locator('#domainGroupTag').click();
    await page.locator('#domainGroupTag').fill('Test');

    // Wait for the suggestion list to appear and then select the domain group
    // Use a more specific approach to handle multiple suggestion lists
    await page.waitForTimeout(500); // Give the UI time to update

    // Click on the first suggestion that contains the text we're looking for
    await page.locator('li').filter({ hasText: /Test Domain Group/ }).first().click();

    // Save and verify modal closes
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).not.toBeVisible();

    // Verify new group appears in list - use a more specific selector
    await expect(page.getByRole('row').filter({ hasText: combinedRulesName }).first()).toBeVisible();
  });

  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already Advanced groups configured
  // WHEN: I check the available options
  // THEN: Each group has an 'Edit' and 'Delete' button available
  await test.step('Verify advanced group action buttons', async () => {
    // Find an advanced group row
    const advancedGroup = page.getByRole('row').filter({ hasText: advancedGroupName }).first();
    await expect(advancedGroup).toBeVisible();

    // Verify Edit and Delete buttons are available
    await expect(advancedGroup.getByRole('button', { name: 'Edit' })).toBeVisible();
    await expect(advancedGroup.getByRole('button', { name: 'Delete' })).toBeVisible();
  });

  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already Advanced groups configured
  // WHEN: I select the 'Edit' option
  // THEN: The 'Edit advanced group' modal is presented
  await test.step('Check edit advanced group modal', async () => {
    // Find and click the Edit button for an advanced group
    const advancedGroup = page.getByRole('row').filter({ hasText: advancedGroupName }).first();
    await advancedGroup.getByRole('button', { name: 'Edit' }).click();

    // Verify the edit modal is displayed with correct title
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).toBeVisible();

    // Verify key elements in the modal
    await expect(page.getByText('Required Information')).toBeVisible();
    await expect(page.getByLabel('Name *')).toBeVisible();

    // Use more specific selectors for the headings
    await expect(page.locator('.heading').filter({ hasText: 'User group rules' })).toBeVisible();
    await expect(page.locator('.heading').filter({ hasText: 'Domain group rules' })).toBeVisible();
  });

  // GIVEN: The 'Edit advanced group' modal is displayed
  // WHEN: I click the 'Close' button
  // THEN: The modal is dismissed
  // AND: No changes to the group have been made
  await test.step('Check edit advanced group modal close', async () => {
    // Click the Close button
    await page.getByRole('button', { name: 'Close' }).last().click();

    // Verify the modal is dismissed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).not.toBeVisible();

    // Verify the group still exists unchanged
    await expect(page.getByText(advancedGroupName, { exact: true }).locator('..')).toBeVisible();
  });

  // GIVEN: The 'Edit advanced group' modal is displayed
  // AND: Either an Include or Exclude user group rule is in place
  // WHEN: I add a group to the 'User groups' field
  // AND: Select the 'Save button
  // THEN: The modal is dismissed
  // AND: The changes have been saved
  await test.step('Add user group to advanced group', async () => {
    // Make sure we're in the Advanced groups section
    await page.getByRole('heading', { name: 'Advanced groups' }).scrollIntoViewIfNeeded();

    // Create a new advanced group with user group rules for testing
    const testAdvGroupName = `Test Adv Group - User Rules ${dayjs().format('YYYYMMDDHHmmss')}`;

    // Open modal and enter name
    await page.getByRole('button', { name: 'Add advanced group' }).click();
    await page.waitForTimeout(500); // Wait for modal to fully appear
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).toBeVisible();

    await page.getByLabel('Name *').fill(testAdvGroupName);

    // Select Include users option
    await page.locator('#sgUserRule2').click();

    // Add a user group (required when Include users is selected)
    await page.locator('#userGroupTag').click();
    await page.locator('#userGroupTag').fill('Test');
    await page.waitForTimeout(500); // Give the UI time to update
    await page.locator('li').filter({ hasText: /Test Group/ }).first().click();

    // Save the group
    await page.getByRole('button', { name: 'Save' }).click();
    await page.waitForTimeout(1000); // Wait for save to complete

    // Make sure we're in the Advanced groups section again
    await page.getByRole('heading', { name: 'Advanced groups' }).scrollIntoViewIfNeeded();

    // Now edit the group to add a user group
    const advGroup = page.locator('[name="__react.AdvancedGroupsPanel"]').getByText(testAdvGroupName, { exact: true }).locator('..');
    await expect(advGroup).toBeVisible();
    await advGroup.getByRole('button', { name: 'Edit' }).click();


    // Verify the edit modal is displayed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).toBeVisible();

    // Add a user group
    await page.locator('#userGroupTag').click();
    await page.locator('#userGroupTag').fill('Test');
    await page.waitForTimeout(500); // Give the UI time to update
    await page.locator('li').filter({ hasText: /Test Group/ }).first().click();

    // Save the changes
    await page.getByRole('button', { name: 'Save' }).click();

    // Verify the modal is dismissed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).not.toBeVisible();


    // Verify the group still exists
    await expect(page.locator('[name="__react.AdvancedGroupsPanel"]').getByText(testAdvGroupName, { exact: true }).locator('..')).toBeVisible();
  });

  // GIVEN: The 'Edit advanced group' modal is displayed
  // AND: Either an Include or Exclude user group rule is in place
  // WHEN: I remove a group from the 'User groups' field
  // AND: Select the 'Save button
  // THEN: The modal is dismissed
  // AND: The changes have been saved
  await test.step('Remove user group from advanced group', async () => {
    // Find and edit the advanced group we just created
    const testAdvGroupName = 'Test Adv Group - User Rules';
    const advGroup = page.locator('[name="__react.AdvancedGroupsPanel"]').getByText(testAdvGroupName).locator('..');
    await expect(advGroup).toBeVisible();
    await advGroup.getByRole('button', { name: 'Edit' }).click();

    // Verify the edit modal is displayed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).toBeVisible();

    // Remove the user group
    await page.locator('tags-input').getByRole('listitem').getByText('×').click();

    // Add a different user group (since we need at least one)
    await page.locator('#userGroupTag').click();
    await page.locator('#userGroupTag').fill('Test Group with Users');
    await page.waitForTimeout(500); // Give the UI time to update
    await page.locator('li').filter({ hasText: /Test Group with Users/ }).first().click();

    // Save the changes
    await page.getByRole('button', { name: 'Save' }).click();

    // Verify the modal is dismissed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).not.toBeVisible();
  });

  // GIVEN: The 'Edit advanced group' modal is displayed
  // AND: Either an Include or Exclude domain group rule is in place
  // WHEN: I add a group to the 'Domain groups' field
  // AND: Select the 'Save button
  // THEN: The modal is dismissed
  // AND: The changes have been saved
  await test.step('Add domain group to advanced group', async () => {
    // Make sure we're in the Advanced groups section
    await page.getByRole('heading', { name: 'Advanced groups' }).scrollIntoViewIfNeeded();

    // Create a new advanced group with domain group rules for testing
    const testAdvDomainGroupName = `Test Adv Group - Domain Rules ${dayjs().format('YYYYMMDDHHmmss')}`;

    // Open modal and enter name
    await page.getByRole('button', { name: 'Add advanced group' }).click();
    await page.waitForTimeout(500); // Wait for modal to fully appear
    await expect(page.getByRole('heading', { level: 1, name: 'Add advanced group' })).toBeVisible();

    await page.getByLabel('Name *').fill(testAdvDomainGroupName);

    // Select Include domain option
    await page.locator('#sgDomainRule2').click();

    // Add a domain group (required when Include domain is selected)
    await page.locator('#domainGroupTag').click();
    await page.locator('#domainGroupTag').fill('Test');
    await page.waitForTimeout(500); // Give the UI time to update
    await page.locator('li').filter({ hasText: /Test Domain Group/ }).first().click();

    // Save the group
    await page.getByRole('button', { name: 'Save' }).click();
    await page.waitForTimeout(1000); // Wait for save to complete

    // Make sure we're in the Advanced groups section again
    await page.getByRole('heading', { name: 'Advanced groups' }).scrollIntoViewIfNeeded();

    // Now edit the group to add a domain group
    const advGroup = page.locator('[name="__react.AdvancedGroupsPanel"]').getByText(testAdvDomainGroupName, { exact: true }).locator('..');
    await expect(advGroup).toBeVisible();
    await advGroup.getByRole('button', { name: 'Edit' }).click();
    await page.waitForTimeout(500); // Wait for modal to fully appear

    // Verify the edit modal is displayed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).toBeVisible();

    // Add a domain group
    await page.locator('#domainGroupTag').click();
    await page.locator('#domainGroupTag').fill('Test');
    await page.waitForTimeout(500); // Give the UI time to update
    await page.locator('li').filter({ hasText: /Test Domain Group/ }).first().click();

    // Save the changes
    await page.getByRole('button', { name: 'Save' }).click();

    // Verify the modal is dismissed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).not.toBeVisible();

    // Verify the group still exists
    await expect(page.locator('[name="__react.AdvancedGroupsPanel"]').getByText(testAdvDomainGroupName, { exact: true }).locator('..')).toBeVisible();
  });

  // GIVEN: The 'Edit advanced group' modal is displayed
  // AND: Either an Include or Exclude domain group rule is in place
  // WHEN: I remove a group from the 'Domain groups' field
  // AND: Select the 'Save button
  // THEN: The modal is dismissed
  // AND: The changes have been saved
  await test.step('Remove domain group from advanced group', async () => {
    // Find and edit the advanced group we just created
    const testAdvDomainGroupName = 'Test Adv Group - Domain Rules';
    const advGroup = page.locator('[name="__react.AdvancedGroupsPanel"]').getByText(testAdvDomainGroupName).locator('..');
    await expect(advGroup).toBeVisible();
    await advGroup.getByRole('button', { name: 'Edit' }).click();

    // Verify the edit modal is displayed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).toBeVisible();

    // Remove the domain group
    await page.locator('tags-input').getByRole('listitem').getByText('×').click();

    // Add a different domain group (since we need at least one)
    await page.locator('#domainGroupTag').click();
    await page.locator('#domainGroupTag').fill('Test Domain Group With Domains');
    await page.waitForTimeout(500); // Give the UI time to update
    await page.locator('li').filter({ hasText: /Test Domain Group With Domains/ }).first().click();

    // Save the changes
    await page.getByRole('button', { name: 'Save' }).click();

    // Verify the modal is dismissed
    await expect(page.getByRole('heading', { level: 1, name: 'Edit advanced group' })).not.toBeVisible();
  });

  // GIVEN: I am on the Groups part of the Admin section
  // AND: There are already Domain/user groups configured
  // WHEN: I select the 'Delete' option
  // THEN: The domain/user group is removed from the list
  await test.step('Delete groups', async () => {
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
    await page.getByRole('button', { name: 'Delete' }).last().click();
    await page.waitForTimeout(500);
  });

  // Close the context and save video recording
  await Promise.all([
    page.video().saveAs(recordingFilePath),
    page.close()
  ]);
  testInfo.attachments.push({
    name: 'video',
    path: recordingFilePath,
    contentType: 'video/webm'
  });
});
