const base = require('@playwright/test');
const { LoginPage } = require('../pages/login.page');
const { NewBookingSearchResultsPage } = require('../pages/newBookingSearchResults.page');
const { NewBookingModalPage } = require('../pages/newBookingModal.page');
const { OutlookEmailPage } = require('../pages/outlookEmail.page');
const { MyBookingsPage } = require('../pages/myBookings.page');

/**
 * @typedef {import('../pages/login.page').LoginPage} LoginPage
 * @typedef {import('../pages/newBookingSearchResults.page').NewBookingSearchResultsPage} NewBookingSearchResultsPage
 * @typedef {import('../pages/newBookingModal.page').NewBookingModalPage} NewBookingModalPage
 * @typedef {import('../pages/outlookEmail.page').OutlookEmailPage} OutlookEmailPage
 * @typedef {import('../pages/myBookings.page').MyBookingsPage} MyBookingsPage
 */


const dayjs = require('dayjs');
const fs = require('fs');

exports.test = base.test.extend({
    // Manual context creation for every test, always with video enabled
    context: async ({ browser }, use, testInfo) => {
        // Use recordVideo.size from project config if available, else undefined
        let recordVideo = { dir: testInfo.outputPath('videos') };
        if (testInfo.project?.use?.video?.size) {
            recordVideo.size = testInfo.project.use.video.size;
        }
        const context = await browser.newContext({
            recordVideo,
            ...testInfo.project.use // inherit other project settings
        });

        try {
            await use(context);
        } finally {
            //console.log('Video handling started');
            const pages = context.pages();
            let idx = 0;
            const projectName = testInfo.project.name;
            let recordingFileFolder;

            // Determine folder based on COA annotation or test title
            if (testInfo.annotations?.some(annotation => annotation.type === 'coa')) {
                let coa = testInfo.annotations.find(annotation => annotation.type === 'coa');
                recordingFileFolder = `Videos/${projectName}/${coa.description}`;
            } else {
                recordingFileFolder = `Videos/${projectName}/${testInfo.title}`;
            }

            for (const page of pages) {
                const video = page.video();
                if (video) {
                    const recordingFileName = `${projectName} - ${testInfo.title}${pages.length > 1 ? `-tab${idx + 1}` : ''} ${dayjs().format('DD-MM-YYYY HH꞉mm꞉ss')}.webm`;
                    const targetFolder = testInfo.status === 'failed'
                        ? `Videos/Failed/${recordingFileFolder}`
                        : recordingFileFolder;

                    fs.mkdirSync(targetFolder, { recursive: true });
                    const recordingFilePath = `${targetFolder}/${recordingFileName}`;
                    await page.close();
                    // Save video before closing anything
                    await video.saveAs(recordingFilePath);
                    //console.log(`Video saved to: ${recordingFilePath}`);

                    testInfo.attachments.push({
                        name: `video${pages.length > 1 ? `-tab${idx + 1}` : ''}`,
                        path: recordingFilePath,
                        contentType: 'video/webm'
                    });
                }
                idx++;
            }
            // Close context after all videos are saved
            await context.close();
            //console.log('Context closed');
        }
    },
    page: async ({ context }, use) => {
        const page = await context.newPage();
        await use(page);
    },
    loginPage: async ({ page }, use) => {
        await use(new LoginPage(page));
    },
    newBookingSearchPage: async ({ page }, use) => {
        await use(new NewBookingSearchResultsPage(page));
    },
    searchResultsPage: async ({ page }, use) => {
        await use(new NewBookingSearchResultsPage(page));
    },
    newBookingModalPage: async ({ page }, use) => {
        await use(new NewBookingModalPage(page));
    },
    outlookEmailPage: async ({ page }, use) => {
        await use(new OutlookEmailPage(page));
    },
    myBookingsPage: async ({ page }, use) => {
        await use(new MyBookingsPage(page));
    }
});

exports.expect = base.expect;