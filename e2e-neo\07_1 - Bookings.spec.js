//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check a empty booking screen', { tag: '@serial', annotation:{type:'coa', description:'Bookings screen - Empty state'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Click Booking link', async () => {
        if (projectViewport.width < 1024){
                    await page.getByRole('button', { name: 'Menu Access menu options' }).click();
        }
        await page.getByRole('link', { name: 'Menu item: Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Validate all expected elements and messages are present', async () => {
        await expect(page.getByRole('heading', { name: 'You have no bookings', exact:true })).toBeVisible();
        await expect(page.getByText('Your current and upcoming bookings will appear here.', {exact:true})).toBeVisible();
        await expect(page.getByRole('button', { name: 'Make new booking', exact:true })).toBeVisible();
    });
    
    await test.step('Compare the empty page against a golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-bookings-emptyPage.png'); 
    });

});

test('Click make new booking from a empty booking screen', { tag: '@serial', annotation:{type:'coa', description:'Bookings screen - Empty state'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Click Booking link', async () => {
        if (projectViewport.width < 1024){
                    await page.getByRole('button', { name: 'Menu Access menu options' }).click();
        }
        await page.getByRole('link', { name: 'Menu item: Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Click Make new booking and validate navigation to search page', async () => {
        await page.getByRole('button', { name: 'Make new booking', exact:true }).click()
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
        await expect(page).toHaveURL('/neo/search');
    }); 
    
});