# Node.js dependencies
node_modules/

# Test output directories and reports
/test-results/
/Videos/
/playwright-report/
/blob-report/
/monocart-report/
/Experiments/Accessibility-Reports/
/Experiments/Artillery Load Tests/results.json
/Experiments/Artillery Load Tests/results.json.html

# Playwright cache
/playwright/.cache/

# Test data files
/test-data/Users/<USER>
/test-data/Users/<USER>
/test-data/Users/<USER>
/test-data/go/Location export.csv
/test-data/go/Users/<USER>
/test-data/go/Users/<USER>
/test-data/go/Users/<USER>
/test-data/go/Seed.json
/test-data/go/Resources.json
/test-data/signage/Users/<USER>
/test-data/signage/Seed.json
/test-data/signage/Users/<USER>
/test-data/fpkiosk/Users/<USER>
/test-data/fpkiosk/Users/<USER>
/test-data/fpkiosk/Users/<USER>
/test-data/fpkiosk/Seed.json
/test-data/neo/Users/<USER>
/test-data/neo/Users/<USER>
/test-data/neo/Users/<USER>
/test-data/neo/Users/<USER>
/test-data/neo/Users/<USER>
/test-data/neo/Seed.json

# Environment configuration
.env

# E2E test data and logs
test-data/e2e/failed_orgs.log
test-data/e2e/Org0_data.json
test-data/e2e/Org1_data.json
test-data/e2e/Org2_data.json
test-data/e2e/Org3_data.json