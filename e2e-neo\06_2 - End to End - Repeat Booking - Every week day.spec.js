//import test and expect functionality

// Video handling is now managed globally in the fixture (pages.fixture.js)
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Create a repeat booking - Every week day', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Create a repeat booking - Every week day' } }, async ({ page, loginPage, outlookEmailPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    // Set projectViewport for use throughout the test
    //const projectViewport = test.info().project.use.viewport;

    test.slow(); //Sets the timeout to triple the project timeout. This is due to accessing the outlook webmail UI.

    const email_user_data = require('../test-data/neo/Users/<USER>');
    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const org = require('../test-data/neo/Seed.json');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(email_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2, 'hour');
    // Create a new dayjs object for the end time to avoid mutation issues
    const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
    const bookingTitle = `Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    const bookingNotes = `Repeat booking test notes - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;

    // Define repeat booking details
    // Create a new dayjs object for the repeat until date to avoid mutation issues
    const repeatUntilDate = dayjs(bookingFromTime).add(2, 'week');

    // Generate a random number for external attendee email
    const randomNumber = Math.floor(Math.random() * 9999);
    const externalAttendeeEmail = `visitor${randomNumber}@test.com`;
    const externalAttendeeName = `Visitor ${randomNumber}`;

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        await expect.soft(page).toHaveScreenshot('Neo-repeat-booking-searchresults.png');
    });

    await test.step('Click Book on Basement Room 2', async () => {
        await searchResultsPage.bookResource('Basement Room 2');
    });

    await test.step('Set the Start time', async () => {
        await newBookingModalPage.setStartTime(bookingFromTime);
    });

    await test.step('Set the End time', async () => {
        await newBookingModalPage.setEndTime(bookingToTime);
    });

    await test.step('Enable repeat booking and set frequency to Every week day', async () => {
        await newBookingModalPage.setupRepeatBooking('Every week day', repeatUntilDate, projectViewport.width);
        await expect.soft(page).toHaveScreenshot('Neo-repeat-booking-setup.png');
    });

    await test.step('Add a title', async () => {
        await newBookingModalPage.setTitle(bookingTitle);
    });

    await test.step('Add some notes', async () => {
        await newBookingModalPage.setNotes(bookingNotes);
    });

    await test.step('Add the internal attendee Standard User', async () => {
        await newBookingModalPage.addInternalAttendee(standard_user_data.FirstName, projectViewport.width);
    });

    await test.step('Add an external attendee', async () => {
        await newBookingModalPage.addExternalAttendee(
            externalAttendeeEmail,
            externalAttendeeName,
            'Contact',
            projectViewport.width
        );
        await expect.soft(page).toHaveScreenshot('Neo-repeat-booking-form.png');
    });

    await test.step('Click book & validate success message', async () => {
        await newBookingModalPage.completeBooking();
        await newBookingModalPage.verifyBookingSuccess(bookingFromTime, bookingToTime, email_user_data.Name, true);
        await expect.soft(page).toHaveScreenshot('Neo-repeat-booking-success.png');
    });

    await test.step('Click the booking button and validate the bookings are listed', async () => {
        // Pass the viewport width to handle mobile/desktop navigation correctly
        await newBookingModalPage.goToBookingsList(projectViewport.width);

        // Add a wait to ensure the page has fully loaded and stabilized
        await page.waitForTimeout(2000);
        await page.waitForLoadState('networkidle');
        await page.waitForLoadState('domcontentloaded');

        // Verify the booking exists
        await myBookingsPage.verifyBookingExists(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
    });

    await test.step('verify booking exists on the week days', async () => {
        // Get all weekday dates between bookingFromTime and repeatUntilDate
        const getDatesArray = (startDate, endDate) => {
            const dates = [];
            let currentDate = dayjs(startDate);

            while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
                // day() returns 0-6 where 0 is Sunday
                // We want only Monday (1) through Friday (5)
                if (currentDate.day() !== 0 && currentDate.day() !== 6) {
                    dates.push(currentDate);
                }
                currentDate = currentDate.add(1, 'day');
            }
            return dates;
        };

        const bookingDates = getDatesArray(bookingFromTime, repeatUntilDate);

        // Verify each weekday booking exists
        for (const date of bookingDates) {
            const bookingTime = date
                .hour(bookingFromTime.hour())
                .minute(bookingFromTime.minute());

            await myBookingsPage.verifyBookingExists(
                bookingTime,
                bookingToTime,
                bookingTitle,
                projectViewport.width
            );
        }
    });

    await test.step('Edit a booking and verify attributes', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        await expect.soft(page).toHaveScreenshot('Neo-repeat-booking-edit.png');

        // Verify booking details
        await expect(newBookingModalPage.titleField).toHaveValue(bookingTitle);
        await expect(newBookingModalPage.notesField).toHaveValue(bookingNotes);

        // Verify attendees
        await expect(page.getByRole('button', { name: 'Email Test Automation User (you) (owner)' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Standard Test Automation User' })).toBeVisible();
        await expect(page.getByRole('button', { name: `${externalAttendeeName} Contact` })).toBeVisible();

        // Verify repeat booking settings
        await expect(page.getByText(`Repeats every week day until ${repeatUntilDate.format('ddd, D MMM YYYY')}`)).toBeVisible();

        // Go back to bookings list
        await newBookingModalPage.closeButton.click();
    });

    await test.step('Login to outlook webmail with the email user', async () => {
        await outlookEmailPage.outlookLogin(email_user_data);
    });

    await test.step('Find expected booking email & validate details are correct', async () => {
        await page.getByLabel(`Has attachments Matrix Booking Basement Room 2 booked from ${bookingFromTime.format('h:mm A, dddd D')}`).getByText('repeats every week day until').click();
        await expect(page.getByLabel('Reading Pane').getByRole('heading')).toContainText(`Basement Room 2 booked from ${bookingFromTime.format('h:mm A, dddd D MMMM YYYY')}`);
        await expect(page.locator('#x_desktopContent')).toContainText('Booking Confirmed');
        await expect.soft(page.locator('#x_desktopContent')).toContainText(`Basement Room 2, ${bookingFromTime.format('h:mma')} - ${bookingToTime.format('h:mma')}, ${bookingFromTime.format('dddd D MMMM YYYY')}`);
        await expect(page.locator('#x_mainMiddleTable')).toContainText(standard_user_data.Name);
        await expect(page.locator('#x_mainMiddleTable')).toContainText(bookingTitle);
        await expect(page.locator('#x_mainMiddleTable')).toContainText('Basement Room 2');
        await expect(page.locator('#x_mainMiddleTable')).toContainText('Basement, Test Building 1');
        await expect(page.locator('#x_mainMiddleTable')).toContainText(bookingFromTime.format('h:mma') + ' - ' + bookingToTime.format('h:mma') + ', ' + bookingFromTime.format('dddd D MMMM YYYY'));
        await expect(page.locator('#x_mainMiddleTable')).toContainText(org.orgName);

        // Verify repeat booking information in email
        await expect(page.locator('#x_mainMiddleTable')).toContainText(`Repeats every week day until ${repeatUntilDate.format('ddd, D MMM YYYY')}`);
        await expect.soft(page).toHaveScreenshot('Neo-repeat-booking-confirmation-email.png');
    });

    await test.step('Clean up - Clear email inbox', async () => {
        if (projectViewport.width < 768) {
            await page.getByLabel('Close').click();
            await page.waitForTimeout(1000);
        }

        await page.keyboard.press('Control+KeyA');
        await page.waitForTimeout(1000);
        await page.keyboard.press('Delete');
        await page.waitForTimeout(1000);
        await page.keyboard.press('Enter');
        await page.waitForTimeout(1000);
    });

    await test.step('Clean up - Cancel all repeat bookings', async () => {
        // Navigate back to the bookings page
        await myBookingsPage.goto();

        // Wait for page load
        await page.waitForLoadState('networkidle');

        // Cancel all instances of the booking - now uses new dialog options
        await myBookingsPage.cancelBooking(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            true, // cancelAllInstances - will click "Cancel all remaining bookings" option
            projectViewport.width
        );
    });

});