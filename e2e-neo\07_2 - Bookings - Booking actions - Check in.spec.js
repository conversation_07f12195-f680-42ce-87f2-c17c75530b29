//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Booking card actions - Check in - Outside window', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - Check in'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(1,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Resource type Meeting Rooms' }).click();
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Desk 4 - Check in', async () => {
        await page.getByRole('button', { name: 'Desk 4 - Check in' }).click();
    });

    await test.step('Set the Start time to 5 mins ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check the actions & messages presented on for the newly created bookings', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('BOOKED')).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText(`Check in from ${bookingFromTime.subtract(5,'minute').format('h:mm A')}`)).toBeVisible();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Check in` })).toBeHidden();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await page.getByRole('button', { name: 'Close panel' }).click();            
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeHidden();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('BOOKED').nth(1)).toBeVisible();
            await expect(page.getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByText(`Check in from ${bookingFromTime.subtract(5,'minute').format('h:mm A')}`).nth(1)).toBeVisible();
        }
    });
    
    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-checkin-outsidewindow.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-checkin-outsidewindow.png');
        }
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});

test('Booking card actions - Check in - Inside window', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - Check in'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(5,'minute')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Resource type Meeting Rooms' }).click();
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Desk 4 - Check in', async () => {
        await page.getByRole('button', { name: 'Desk 4 - Check in' }).click();
    });

    await test.step('Set the Start time to 5 mins ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check the actions & messages presented on for the newly created booking', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('CHECK IN NOW')).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByRole('img', { name: 'Check in info' })).toBeHidden();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in` })).toBeVisible();
            await page.getByRole('button', { name: 'Close panel' }).click();            
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            
            await expect(page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('CHECK IN NOW').nth(1)).toBeVisible();
            await expect(page.getByRole('img', { name: 'Check in info' })).toBeHidden();
        }
    });
    
    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-checkin-insidewindow.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-checkin-insidewindow.png');
        }
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});

test('Booking card actions - Check in - Checked in before booking start', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - Check in'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(5,'minute')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Resource type Meeting Rooms' }).click();
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Desk 4 - Check in', async () => {
        await page.getByRole('button', { name: 'Desk 4 - Check in' }).click();
    });

    await test.step('Set the Start time to 5 mins ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check in the new booking', async () => {
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Check in' }).click();
            await page.getByRole('button', { name: 'Close panel' }).click();
        } else{
            await page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }  
    })
    
    await test.step('Check the actions & messages presented on for the newly created booking', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('CHECKED IN')).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByRole('img', { name: 'Check in info' })).toBeHidden();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in` })).toBeHidden();
            await page.getByRole('button', { name: 'Close panel' }).click();            
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeHidden();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('CHECKED IN').nth(1)).toBeVisible();
            await expect(page.getByRole('img', { name: 'Check in info' })).toBeHidden();
        }
    });
    
    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-checkin-checkedinbeforestart.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-checkin-checkedinbeforestart.png');
        }
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});

test('Booking card actions - Check in - Booking time started', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - Check in'} }, async ({ page, loginPage }) => {

    test.slow(); //Sets the timeout to triple the project timeout. This is due to waiting for a booking to start.

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(1,'minute');
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Resource type Meeting Rooms' }).click();
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Desk 4 - Check in', async () => {
        await page.getByRole('button', { name: 'Desk 4 - Check in' }).click();
    });

    await test.step('Set the Start time to 1 mins ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check in the new booking', async () => {
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Check in' }).click();
            await page.getByRole('button', { name: 'Close panel' }).click();
        } else{
            await page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }  
    });

    await test.step('Wait 1 minute & reload the page to ensure the checked in bookings start time has elapsed', async () => {
        await page.waitForTimeout(60000);
        page.reload();
    });
    
    await test.step('Check the actions & messages presented on for the newly created booking', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('CHECKED IN')).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByRole('img', { name: 'Check in info' })).toBeHidden();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `End now` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeHidden();
            await expect(page.getByRole('button', { name: `Check in` })).toBeHidden();
            await page.getByRole('button', { name: 'Close panel' }).click();            
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeHidden();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeHidden();
            await expect(page.getByRole('button', { name: `End now ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('CHECKED IN').nth(1)).toBeVisible();
            await expect(page.getByRole('img', { name: 'Check in info' })).toBeHidden();
        }
    });
    
    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-checkin-checkedinbookingstarted.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-checkin-checkedinbookingstarted.png');
        }
    });

    await test.step('Clean up - End booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'End now' }).click();
        } else {
            await page.getByRole('button', { name: `End now ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'End now' }).click();
    });

});

test('Booking card actions - Check in with auto cancel', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - Check in with auto cancel'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(5,'minute')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Resource type Meeting Rooms' }).click();
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Desk 5 - Check in with Auto Cancel', async () => {
        await page.getByRole('button', { name: 'Desk 5 - Check in with Auto Cancel' }).click();
    });

    await test.step('Set the Start time to 5 mins ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check the actions & messages presented on for the newly created bookings', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('CHECK IN NOW')).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText(`Check in by ${bookingFromTime.add(1,'minute').format('h:mm A')}`)).toBeVisible();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in` })).toBeVisible();
            await page.getByRole('button', { name: 'Close panel' }).click();            
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('CHECK IN NOW').nth(1)).toBeVisible();
            await expect(page.getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByText(`Check in by ${bookingFromTime.add(1,'minute').format('h:mm A')}`).nth(1)).toBeVisible();
        }
    });
    
    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-checkin-withautocancel.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-checkin-withautocancel.png');
        }
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});

test('Booking card actions - Onsite check in', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - On site check in only'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(5,'minute')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Resource type Meeting Rooms' }).click();
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Desk 6 - Onsite check in', async () => {
        await page.getByRole('button', { name: 'Desk 6 - Onsite check in' }).click();
    });

    await test.step('Set the Start time to 5 mins ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check the actions & messages presented on for the newly created bookings', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('CHECK IN NOW')).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('Check in using on-site facilities')).toBeVisible();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in` })).toBeHidden();
            await page.getByRole('button', { name: 'Close panel' }).click();            
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeHidden();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('CHECK IN NOW').nth(1)).toBeVisible();
            await expect(page.getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByText(`Check in using on-site facilities`).nth(1)).toBeVisible();
        }
    });
    
    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-checkin-onsite.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-checkin-onsite.png');
        }
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});

test('Booking card actions - Onsite check in with auto cancel', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - On site check in only'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(5,'minute')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Resource type Meeting Rooms' }).click();
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Desk 7 - Onsite check in with Auto Cancel', async () => {
        await page.getByRole('button', { name: 'Desk 7 - Onsite check in with Auto Cancel' }).click();
    });

    await test.step('Set the Start time to 5 mins ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check the actions & messages presented on for the newly created bookings', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('CHECK IN NOW')).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText(`Check in using on-site facilities by ${bookingFromTime.add(1,'minute').format('h:mm A')}`)).toBeVisible();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in` })).toBeHidden();
            await page.getByRole('button', { name: 'Close panel' }).click();            
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Check in ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeHidden();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('CHECK IN NOW').nth(1)).toBeVisible();
            await expect(page.getByRole('img', { name: 'Check in info' })).toBeVisible();
            await expect(page.getByText(`Check in using on-site facilities by ${bookingFromTime.add(1,'minute').format('h:mm A')}`).nth(1)).toBeVisible();
        }
    });
    
    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-checkin-onsitewithautocancel.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-checkin-onsitewithautocancel.png');
        }
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});