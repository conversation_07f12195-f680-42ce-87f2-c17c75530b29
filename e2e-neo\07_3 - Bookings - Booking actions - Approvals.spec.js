//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Booking card actions - Require approval', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - Approval needed'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(3,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 5 - Need approval', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 5 - Need approval' }).click();
    });

    await test.step('Set the Start time to an 3 hours ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Check the actions presented on for the newly created bookings', async () => {         
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('AWAITING APPROVAL')).toBeVisible();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await page.getByRole('button', { name: 'Close panel' }).click();
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('AWAITING APPROVAL').nth(1)).toBeVisible();
        }
    });

    await test.step('Compare the booking card against a golden file image', async () => {
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-awaitingapproval.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-awaitingapproval.png');
        }
    });
    
    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});

test('Booking card actions - Approved', { tag: '@serial', annotation:{type:'coa', description:'Booking screen - Booking statuses & actions - Approval needed'} }, async ({ page, loginPage }) => {

    const admin_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(admin_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(3,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${admin_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 5 - Need approval', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 5 - Need approval' }).click();
    });

    await test.step('Set the Start time to an 3 hours ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
    });

    await test.step('Swap to old web app UI and approve the booking', async () => {
        await page.goto('/ui/#/all-bookings');
        await page.getByRole('button', { name: ' Search' }).click();
        await page.getByRole('button', { name: 'Approve ctrl.' }).click();
        await page.getByRole('button', { name: 'Approve' }).click();
    });

    await test.step('Swap back to Neo Booking screen and validate new approval state', async () => {
        await page.goto('/neo/my-bookings');
        
        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('APPROVED')).toBeVisible();
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByRole('button', { name: `Cancel booking` })).toBeVisible();
            await page.getByRole('button', { name: 'Close panel' }).click();
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('APPROVED').nth(1)).toBeVisible();
        }

    });

    await test.step('Compare the booking card against a golden file image', async () => {  
        if (projectViewport.width < 768){
            await expect.soft(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toHaveScreenshot('Neo-bookingcard-approved.png');
        } else {
            await expect.soft(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}${bookingTitle}`).nth(1)).toHaveScreenshot('Neo-bookingcard-approved.png');
        }   
    });
    
    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});