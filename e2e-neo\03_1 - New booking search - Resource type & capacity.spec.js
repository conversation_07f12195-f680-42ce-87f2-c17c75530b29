//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check new booking search page', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    let timeNow = dayjs();
    let next30minTimeSlot = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');

    await test.step('Validate expected UI elements are presented with expected values', async () => {
        await expect(newBookingSearchPage.mainHeading).toBeVisible();
        await expect(newBookingSearchPage.resourceTypeField).toBeVisible();
        await expect(newBookingSearchPage.dateField).toBeVisible();
        await expect(newBookingSearchPage.locationField).toBeVisible();
        await expect(newBookingSearchPage.startTimeField).toBeVisible();
        await expect(newBookingSearchPage.startTimeField).toHaveValue(`${next30minTimeSlot.format('h:mm A')}`);
        await expect(newBookingSearchPage.endTimeField).toBeVisible();
        await expect(newBookingSearchPage.endTimeField).toHaveValue(`${next30minTimeSlot.add(1, 'hour').format('h:mm A')}`);
        await expect(newBookingSearchPage.capacityField).toBeVisible();
        await expect(newBookingSearchPage.searchButton).toBeVisible();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-search-Page.png');
    });

});

test('Resource type - open, check elements and change value', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Resource type'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    
    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Check resource type is shown and has default value Meeting Rooms', async () => {
        await expect(newBookingSearchPage.resourceTypeField).toBeVisible();
    });

    await test.step('Click resource type field & validate options listed', async () => {
        await newBookingSearchPage.resourceTypeField.click();
        await expect(newBookingSearchPage.meetingRoomOption.nth(1)).toBeVisible();
        await expect(newBookingSearchPage.officesOption).toBeVisible();
        await expect(newBookingSearchPage.bedroomsOption).toBeVisible();
        await expect(page.locator('span:text("Desks"):visible')).toBeVisible();
        await expect(page.locator('span:text("People"):visible')).toBeVisible();
        await expect(page.locator('span:text("Equipment"):visible')).toBeVisible();
        await expect(page.locator('span:text("Access Passes"):visible')).toBeVisible();
        await expect(page.locator('span:text("Privacy Booths"):visible')).toBeVisible();
        await expect(page.locator('span:text("Parking Spaces"):visible')).toBeVisible();
        await expect(page.locator('span:text("Transport"):visible')).toBeVisible();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-search-resourcetype.png');
    });

    await test.step('Select a new value & check selection persists', async () => {
        await page.locator('span:text("Desks"):visible').locator('..').click();
        await expect(page.getByRole('button', { name: 'Resource type Desks' })).toBeVisible();
    });

});

test('Resource type - change to half day', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Resource type'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    
    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Change resource type to a half day type resource (Desks)', async () => {
        await newBookingSearchPage.resourceTypeField.click();
        await newBookingSearchPage.deskOption.click();
    });

    await test.step('Check Time fields have updated for a half day type resource', async () => {
        await expect(page.getByRole('button', { name: 'Time All day' })).toBeVisible();
        await expect(newBookingSearchPage.startTimeField).toBeHidden();
        await expect(newBookingSearchPage.endTimeField).toBeHidden();
    });

    await test.step('Check time options available', async () => {
        await page.getByRole('button', { name: 'Time All day' }).click();
        await expect(page.locator('span:text("AM"):visible')).toBeVisible();
        await expect(page.locator('span:text("PM"):visible')).toBeVisible();
    })

});

test('Resource type - change to full day', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Resource type'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    
    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Change resource type to a full day type resource (Bedrooms)', async () => {
        await newBookingSearchPage.resourceTypeField.click();
        await newBookingSearchPage.bedroomsOption.click();
    });

    await test.step('Check Time fields have updated for a full day type resource (no time field shown)', async () => {
        await expect(newBookingSearchPage.startTimeField).toBeHidden();
        await expect(newBookingSearchPage.endTimeField).toBeHidden();
        await expect(page.getByRole('button', { name: 'Time All day' })).toBeHidden();
    });

});

test('Resource type - change to resource with no capacity', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Resource type'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    
    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Change resource type that has no capacity (Offices)', async () => {
        await newBookingSearchPage.resourceTypeField.click();
        await newBookingSearchPage.officesOption.click()
    });

    await test.step('Check capacity field is not shown', async () => {
        await expect(newBookingSearchPage.capacityField).toBeHidden();
    });

});

test('Capacity - open, check elements and change value', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Capacity'} }, async ({ page, loginPage, newBookingSearchPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    
    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Check capacity field is shown and has default value Any', async () => {
        await expect(newBookingSearchPage.capacityField).toBeVisible();
    });

    await test.step('Click capacity field & validate options listed', async () => {
        await newBookingSearchPage.capacityField.click();
        await expect(newBookingSearchPage.capacityAnyOption).toBeVisible();
        await expect(newBookingSearchPage.capacity1Option).toBeVisible();
        await expect(newBookingSearchPage.capacity5Option).toBeVisible();
        await expect(newBookingSearchPage.capacity10Option).toBeVisible();
        await expect(newBookingSearchPage.capacity20Option).toBeVisible();
        await expect(newBookingSearchPage.capacity50Option).toBeVisible();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-search-capacity.png');
    });

    await test.step('Select a new value & check selection persists', async () => {
        await newBookingSearchPage.capacity10Option.locator('..').click()
        await expect(page.getByRole('button', { name: 'Capacity 10 or more' })).toBeVisible();
    });

});