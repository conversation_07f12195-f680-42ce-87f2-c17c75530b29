//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Make a standard booking in a Meeting Room', { tag: '@serial', annotation:{type:'coa', description:'End to end - Create a standard booking'} }, async ({ page, loginPage, outlookEmailPage }) => {
    
    test.slow(); //Sets the timeout to triple the project timeout. This is due to accessing the outlook webmail UI.

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const email_user_data = require('../test-data/neo/Users/<USER>');
    const org = require('../test-data/neo/Seed.json');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(email_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    const bookingNotes = `Test booking notes - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 2' }).click();
    });

    await test.step('Set the Start time to an 2 hours ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Add some notes', async () => { 
        await page.getByLabel('Notes').click();
        await page.getByLabel('Notes').fill(bookingNotes);
    });
    
    await test.step('Add a catering option', async () => {
        await page.getByRole('button', { name: 'Catering' }).click();
        await page.getByRole('button', { name: 'Add catering Add catering' }).click();
        await page.getByRole('button', { name: 'Option Select catering' }).click();
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Catering option 2' }).click();
        } else {
            await page.getByRole('option', { name: 'Catering option 2' }).click();
        }

    });
    
    await test.step('Add a equipment option', async () => {
        await page.getByRole('button', { name: 'Equipment' }).click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
        await page.getByRole('button', { name: 'Option Select equipment' }).click();
   
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Equipment option 2' }).click();
        } else {
            await page.getByRole('option', { name: 'Equipment option 2' }).click();
        }

    });

    await test.step('Add a services option', async () => {
        await page.getByRole('button', { name: 'Services' }).click();
        await page.getByRole('button', { name: 'Add service Add service' }).click();
        await page.getByRole('button', { name: 'Option Select service' }).click();

        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Services option 2' }).click();
        } else {
            await page.getByRole('option', { name: 'Services option 2' }).click();
        }

    });

    await test.step('Add a room layout', async () => {
        await page.getByRole('button', { name: 'Room layout' }).click();
        await page.getByLabel('Cabaret').click();
    });
    
    
    await test.step('Add the internal attendee Standard User', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Add attendees Add attendees' }).click();
            await page.getByRole('combobox', { name: 'Search by name or email' }).fill(standard_user_data.FirstName);
            await page.getByRole('option', {name: standard_user_data.Name}).click();
            await page.getByRole('button', { name: 'Done' }).click();
        } else {
            await page.getByPlaceholder('Search by name or email address').fill(standard_user_data.FirstName);
            await page.getByRole('option', {name: standard_user_data.Name}).click();
        }
 
    });

    await test.step('Add the external attendee Visitor Contact', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Add attendees Add attendees' }).click();
            await page.getByRole('combobox', { name: 'Search by name or email' }).fill(`Visitor${org.seed}`);
            await page.getByRole('option', {name: `Visitor${org.seed}`}).click();
            await page.getByRole('button', { name: 'Done' }).click();
        } else {
            await page.getByPlaceholder('Search by name or email address').fill(`Visitor${org.seed}`);
            await page.getByRole('option', {name: `Visitor${org.seed}`}).click();
        }
 
    });

    await test.step('Click book & validate success message', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('heading', { name: 'Basement Room 2 booked' })).toBeVisible();
        await expect(page.getByRole('img', { name: 'Booking successful' })).toBeVisible();
        await expect(page.getByText(`${bookingFromTime.format('ddd, D MMM, h:mm A')} - ${bookingToTime.format('h:mm A')}`)).toBeVisible();
        await expect(page.getByText('Basement Room 2, Basement,')).toBeVisible();
        await expect(page.getByLabel('Basement Room 2 booked').getByText(email_user_data.Name)).toBeVisible();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Make another booking' })).toBeVisible();
    });

    await test.step('Click the booking button and validate the new booking is listed on the booking page', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page.getByRole('heading', { name: `Today, ${bookingFromTime.format('dddd DD MMMM')}` })).toBeVisible();

        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` })).toBeVisible();

            await expect(page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).getByText('BOOKED')).toBeVisible();
        } else {
            await expect(page.getByText(`${bookingFromTime.format('h:mm A')}-${bookingToTime.format('h:mm A')}`).nth(1)).toBeVisible();
            await expect(page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`})).toBeVisible();
            await expect(page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` })).toBeVisible();
            await expect(page.getByText('BOOKED').nth(1)).toBeVisible();
        }
    });

    await test.step('Click edit booking and validate the main booking options are as expected', async () => {

        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await expect(page.getByLabel(`Date ${bookingFromTime.format('ddd, D MMM')} expandable`).nth(1)).toBeVisible();
        } else {
            await page.getByRole('button', { name: `Edit booking ${bookingFromTime.format('h:mm A ddd DD')}`}).click();
            await expect(page.getByRole('button', { name: `Date ${bookingFromTime.format('ddd, D MMM')} expandable` })).toBeVisible();
        }

        await expect(page.getByText('Basement Room 2', { exact: true })).toBeVisible(); 
        await expect(page.getByLabel('start-time')).toHaveValue(`${bookingFromTime.format('h:mm A')}`);
        await expect(page.getByLabel('end-time')).toHaveValue(`${bookingToTime.format('h:mm A')}`);
        await expect(page.getByLabel('Title')).toHaveValue(`${bookingTitle}`);
        await expect(page.getByLabel('Notes')).toHaveValue(`${bookingNotes}`);
    });

    await test.step('Validate attendees and owner are as expected', async () => {
        await expect(page.getByLabel('Number of attendees')).toHaveValue('3');
        await expect(page.getByRole('button', { name: 'Email Test Automation User (you) (owner)' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Standard Test Automation User' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Visitor Contact' })).toBeVisible();
    });

    await test.step('Validate options are as expected', async () => {
        await expect(page.getByRole('button', { name: 'Services (1)' })).toBeVisible();
        await page.getByRole('button', { name: 'Services (1)' }).click();
        await expect(page.getByRole('button', { name: 'Option Services option 2' })).toBeVisible();
        await expect(page.getByLabel('Services (1)').getByLabel('start-time')).toHaveValue(`${bookingFromTime.format('h:mm A')}`);
        await expect(page.getByLabel('Services (1)').getByLabel('end-time')).toHaveValue(`${bookingToTime.format('h:mm A')}`);
        await page.getByRole('button', { name: 'Room layout' }).click();
        await expect(page.getByLabel('Cabaret')).toBeVisible();
        await expect(page.getByRole('button', { name: 'Catering (1)' })).toBeVisible();
        await page.getByRole('button', { name: 'Catering (1)' }).click();
        await expect(page.getByRole('button', { name: 'Option Catering option 2' })).toBeVisible();
        await expect(page.getByLabel('Catering (1)').getByLabel('start-time')).toHaveValue(`${bookingFromTime.format('h:mm A')}`);
        await expect(page.getByLabel('Catering (1)').getByLabel('end-time')).toHaveValue(`${bookingToTime.format('h:mm A')}`);
        await expect(page.getByRole('button', { name: 'Equipment (1)' })).toBeVisible();
        await page.getByRole('button', { name: 'Equipment (1)' }).click();
        await expect(page.getByRole('button', { name: 'Option Equipment option 2' })).toBeVisible();
        await expect(page.getByLabel('Equipment (1)').getByLabel('start-time')).toHaveValue(`${bookingFromTime.format('h:mm A')}`);
        await expect(page.getByLabel('Equipment (1)').getByLabel('end-time')).toHaveValue(`${bookingToTime.format('h:mm A')}`);
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByLabel('Cancel', { exact: true }).click();
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

    await test.step('Login to outlook webmail with the email user', async () => {   
        await outlookEmailPage.outlookLogin(email_user_data);
    });

    await test.step('Find expected booking email & validate details are correct', async () => {
        await page.getByLabel(`Has attachments Matrix Booking Basement Room 2 booked from ${bookingFromTime.format('h:mm A, dddd D')}`).getByText('Matrix Booking').click();
        await expect(page.getByLabel('Reading Pane').getByRole('heading')).toContainText(`Basement Room 2 booked from ${bookingFromTime.format('h:mm A, dddd D MMMM YYYY')}`);
        await expect(page.locator('#x_desktopContent')).toContainText('Booking Confirmed');
        await expect(page.locator('#x_desktopContent')).toContainText(`Basement Room 2, ${bookingFromTime.format('h:mma')} - ${bookingToTime.format('h:mma')}, ${bookingFromTime.format('dddd D MMMM YYYY')}`);
        await expect(page.locator('#x_mainMiddleTable')).toContainText(standard_user_data.Name);
        await expect(page.locator('#x_mainMiddleTable')).toContainText(bookingTitle);
        await expect(page.locator('#x_mainMiddleTable')).toContainText('Basement Room 2');
        await expect(page.locator('#x_mainMiddleTable')).toContainText('Basement, Test Building 1');
        await expect(page.locator('#x_mainMiddleTable')).toContainText(bookingFromTime.format('h:mma') + ' - ' + bookingToTime.format('h:mma') + ', '+ bookingFromTime.format('dddd D MMMM YYYY'));
        await expect(page.locator('#x_mainMiddleTable')).toContainText(org.orgName);
    });
    
    await test.step('Clean up - Clear email inbox', async () => {
        if (projectViewport.width < 768){
            await page.getByLabel('Close').click();
            await page.waitForTimeout(1000);
        }
        
        await page.keyboard.press('Control+KeyA');
        await page.waitForTimeout(1000);
        await page.keyboard.press('Delete');
        await page.waitForTimeout(1000);
        await page.keyboard.press('Enter');
        await page.waitForTimeout(1000);
    });
    
});

test('Check confirmation modal - Success', { tag: '@serial', annotation:{type:'coa', description:'New booking - Confirmation modal - Success'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(3,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 2', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 2' }).click();
    });

    await test.step('Set the Start time to an 3 hours ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book & validate all elements present on success message', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('img', { name: 'Booking successful' })).toBeVisible();

        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
        } else {
            await expect(page.getByRole('button', { name: 'Close booking confirmation' })).toBeVisible();
        }

        await expect(page.getByRole('heading', { name: 'Basement Room 2 booked' })).toBeVisible();
        await expect(page.getByText('Time', { exact: true })).toBeVisible();
        await expect(page.getByText(`${bookingFromTime.format('ddd, D MMM, h:mm A')} - ${bookingToTime.format('h:mm A')}`)).toBeVisible();
        await expect(page.getByLabel('Basement Room 2 booked').getByText('Location')).toBeVisible();
        await expect(page.getByText('Basement Room 2, Basement, Test Building 1')).toBeVisible();
        await expect(page.getByText('Owner')).toBeVisible();
        await expect(page.getByLabel('Basement Room 2 booked').getByText(standard_user_data.Name)).toBeVisible();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Make another booking' })).toBeVisible();
    });

    await test.step('Compare the success modal against a golden file image', async () => {
        await expect.soft(page.getByText(`Basement Room 2 bookedTime${bookingFromTime.format('ddd')}`)).toHaveScreenshot('Neo-booking-successmessage.png');
        });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});

test('Check confirmation modal - Requires approval', { tag: '@serial', annotation:{type:'coa', description:'New booking - Confirmation modal - Approval required'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    //Define details for a new booking
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(3,'hour')
    const bookingToTime = bookingFromTime.add(1,'hour');
    const bookingTitle = `Test booking title - Made by ${standard_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    
    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 5 - Need approval', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 5 - Need approval' }).click();
    });

    await test.step('Set the Start time to an 3 hours ahead', async () => {
        await page.getByLabel('start-time').click();
        await page.getByLabel('start-time').fill(`${bookingFromTime.format('h:mm A')}`);
        await page.keyboard.press('Enter');
    });

    await test.step('Add a title', async () => { 
        await page.getByLabel('Title').click();
        await page.getByLabel('Title').fill(bookingTitle);
    });

    await test.step('Click book & validate all elements present on success message', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();
        await expect(page.getByRole('img', { name: 'Booking awaiting approval' })).toBeVisible();
        await expect(page.getByText('AWAITING APPROVAL')).toBeVisible();

        if (projectViewport.width < 768){
            await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
        } else {
            await expect(page.getByRole('button', { name: 'Close booking confirmation' })).toBeVisible();
        }

        await expect(page.getByRole('heading', { name: 'Basement Room 5 - Need approval booked' })).toBeVisible();
        await expect(page.getByText('Time', { exact: true })).toBeVisible();
        await expect(page.getByText(`${bookingFromTime.format('ddd, D MMM, h:mm A')} - ${bookingToTime.format('h:mm A')}`)).toBeVisible();
        await expect(page.getByLabel('Basement Room 5 - Need approval booked').getByText('Location')).toBeVisible();
        await expect(page.getByText('Basement Room 5 - Need approval, Basement, Test Building 1')).toBeVisible();
        await expect(page.getByText('Owner')).toBeVisible();
        await expect(page.getByLabel('Basement Room 5 - Need approval booked').getByText(standard_user_data.Name)).toBeVisible();
        await expect(page.getByRole('button', { name: 'Bookings' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Make another booking' })).toBeVisible();
    });

    await test.step('Compare the success modal against a golden file image', async () => {
        await expect.soft(page.getByText(`Basement Room 5 - Need approval bookedAWAITING APPROVALTime${bookingFromTime.format('ddd, D MMM, h:mm A')}`)).toHaveScreenshot('Neo-booking-successmessageapproval.png');
    });

    await test.step('Click the booking button and validate navigation to booking screen', async () => {
        await page.getByRole('button', { name: 'Bookings' }).click();
        await expect(page).toHaveURL('/neo/my-bookings');
    });

    await test.step('Clean up - Cancel booking etc', async () => {
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: `${bookingFromTime.format('h:mm A')} - ${bookingToTime.format('h:mm A')} ${bookingTitle}` }).click();
            await page.getByRole('button', { name: 'Cancel booking' }).click();
        } else {
            await page.getByRole('button', { name: `Cancel booking ${bookingFromTime.format('h:mm A ddd DD')}` }).click();
        }
        
        await page.getByRole('button', { name: 'Cancel booking' }).click();
    });

});