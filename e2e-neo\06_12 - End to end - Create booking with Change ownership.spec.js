import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    projectViewport = testInfo.project.use.viewport;
});

test('Create booking with changed ownership', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Create a standard booking with Change ownership' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {

    const location_manager_user_data = require('../test-data/neo/Users/<USER>');
    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login as location manager', async () => {
        await loginPage.simpleLogin(location_manager_user_data);
    });

    // Define booking details
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2, 'hour');
    const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
    const bookingTitle = `Owner Change Test - Made by ${location_manager_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    const bookingNotes = `Owner change test notes - Made by ${location_manager_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await searchResultsPage.bookResource('Basement Room 1');
        await expect(page.getByLabel('New booking', { exact: true }).getByText('New booking')).toBeVisible();
    });

    await test.step('Set booking times', async () => {
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
    });

    await test.step('Click change & enter the name of a known user', async () => {
        await newBookingModalPage.changeOwnerButton.click();
        await newBookingModalPage.changeOwnerSearchField.fill(standard_user_data.Name);
        await expect(page.getByRole('option', { name: standard_user_data.Name })).toContainText(standard_user_data.Name);
        await expect(page.getByRole('option', { name: standard_user_data.Name })).toContainText(standard_user_data.Email);
        await page.getByRole('option', { name: standard_user_data.Name }).click();
    });

    await test.step('Add booking details', async () => {
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.setNotes(bookingNotes);
    });

    await test.step('Create booking & validate success message', async () => {
        await newBookingModalPage.completeBooking();
        await newBookingModalPage.verifyBookingSuccess(bookingFromTime, bookingToTime, standard_user_data.Name);
        await expect.soft(page).toHaveScreenshot('Neo-changed-owner-booking-success.png');

        //if only small view click this button

        if (projectViewport.width < 768) {
            await newBookingModalPage.closeButton.click();
        } else {
            await newBookingModalPage.closeBookingConfirmationButton.click();
        }
    });



    // Verify the booking in My Bookings for the location manager
    await test.step('Verify the booking exists for the location manager', async () => {
        await myBookingsPage.goto();
        await myBookingsPage.verifyBookingExists(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            projectViewport.width
        );
    });

    await test.step('Switch to standard user to verify booking ownership', async () => {
        //if small or medium click menu first
        if (projectViewport.width < 1024) {
            await page.getByRole('button', { name: 'Menu Access menu options' }).click();
            await page.getByRole('link', { name: 'Log out' }).click();
        } else {
            await page.getByRole('button', { name: 'LT Access log out button' }).click();
            await page.getByRole('link', { name: 'Menu item: Log out' }).click();
        }
        await expect(page.getByText('Log in to your account')).toBeVisible();
        await loginPage.simpleLogin(standard_user_data);
        //wait for user to log in
        await expect(page.getByRole('button', { name: 'Search' })).toBeVisible();

        await myBookingsPage.goto();

        // Verify the booking exists for the standard user
        await myBookingsPage.verifyBookingExists(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            projectViewport.width
        );
    });

    await test.step('Verify booking details via edit', async () => {
        await myBookingsPage.editBooking(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            projectViewport.width
        );

        // Verify booking details
        await expect(newBookingModalPage.titleField).toHaveValue(bookingTitle);
        await expect(newBookingModalPage.notesField).toHaveValue(bookingNotes);
        await expect(page.getByText(`Booking owner${standard_user_data.Name}`)).toBeVisible();

        // Close the edit modal
        await newBookingModalPage.closeButton.click();
    });

    await test.step('Clean up - Cancel the booking', async () => {
        await myBookingsPage.cancelBooking(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            false,
            projectViewport.width
        );
    });
});
