{"name": "playwright-matrix", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"neo-build": "npx playwright test --project=setup-neo --headed --workers=1", "neo-parallel": "npx playwright test --project=e2e-neo* --workers=6 -g parallel --no-deps", "neo-serial": "npx playwright test --project=e2e-neo* --headed  --workers=1 -g serial --no-deps", "neo": "npm run neo-build && npm run neo-parallel && npm run neo-serial", "webapp": "npx playwright test --project=e2e --headed --workers=3", "webappUpload": "node ./test-data/updateJiraWithEvidence.js Webapp", "webappRegression": "node -e \"console.log(process.argv); const ticketId = process.argv[1]; if (!ticketId) { console.error('Error: Missing ticket ID'); process.exit(1); } else { try { require('child_process').execSync('npm run webapp', { stdio: 'inherit' }); require('child_process').execSync('npm run webappUpload -- ' + ticketId, { stdio: 'inherit' }); } catch (error) { console.error('Webapp failed, skipping upload...'); process.exit(1); } }\"", "neoUpload": "node ./test-data/updateJiraWithEvidence.js neo", "neoRegression": "node -e \"const ticketId = process.argv[1]; if (!ticketId) { console.error('Error: Missing ticket ID'); process.exit(1); } else { try { require('child_process').execSync('npm run neo', { stdio: 'inherit' }); require('child_process').execSync('npm run neoUpload -- ' + ticketId, { stdio: 'inherit' }); } catch (error) { console.error('Neo tests failed, skipping upload...'); process.exit(1); } }\""}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.49.1", "@types/node": "^20.11.30", "monocart-reporter": "^2.9.12"}, "dependencies": {"@axe-core/playwright": "^4.9.1", "axe-html-reporter": "^2.2.5", "axios": "^1.7.9", "dayjs": "^1.11.12", "dotenv": "^16.4.7", "playwright": "^1.49.1"}}