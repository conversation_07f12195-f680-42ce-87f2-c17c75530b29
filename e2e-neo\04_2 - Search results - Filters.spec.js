//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check listed filters', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Validate the expected filter elements are presented', async () => {
        if (projectViewport.width > 1023){
            await expect(page.getByLabel('Filter by')).toBeVisible();
            await expect(page.getByRole('button', { name: 'Location', exact:true})).toBeVisible();
            await expect(page.getByRole('button', { name: 'Facilities', exact:true})).toBeVisible();
            await expect(page.getByRole('button', { name: 'Layouts', exact:true})).toBeVisible();
        }
        else{
            await expect(page.getByRole('button', { name: 'All filters' })).toBeVisible();
            await page.getByRole('button', { name: 'All filters' }).click();
            await expect(page.getByRole('heading', { name: 'All filters' })).toBeVisible();    
            await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Apply' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Location'})).toBeVisible();
            await expect(page.getByRole('button', { name: 'Facilities'})).toBeVisible();
            await expect(page.getByRole('button', { name: 'Layouts'})).toBeVisible();
        }  

            await expect(page.getByRole('button', { name: 'Clear all' })).toBeVisible();
            
    });

    await test.step('Compare filters against golden file', async () => {
        if (projectViewport.width < 1024){
            await expect.soft(page).toHaveScreenshot('Neo-searchresults-filters.png');
        }
        else{
            await expect.soft(page.getByLabel('Filter by')).toHaveScreenshot('Neo-searchresults-filters.png');
        }
        
    });
    
});

test('Check location filter', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters - Location'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Open the filter menu if in medium or small view', async () => {
        if (projectViewport.width < 1024){
            await expect(page.getByRole('button', { name: 'Filter by location' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'All filters' })).toBeVisible();
            await page.getByRole('button', { name: 'Filter by location' }).click();
            await expect(page.getByRole('heading', { name: 'Filter by' })).toBeVisible();    
        }            
    });

    await test.step('Check the location filter is showing the expected option Testing Building 1', async () => {
        await expect(page.getByRole('treeitem')).toContainText('Test Building 1');
        await expect(page.getByRole('treeitem', { name: 'Test Building 1' }).locator('span').first()).toBeVisible()
    });

    await test.step('Expand the location filter & check listed options', async () => {
        await page.getByRole('treeitem', { name: 'Test Building 1' }).locator('img').first().click();
        await expect(page.getByRole('treeitem', { name: 'Basement' })).toBeVisible();
        await expect(page.getByRole('treeitem', { name: 'Ground Floor' })).toBeVisible();
    });

    await test.step('Compare fully open location filter against golden file', async () => { 
       await expect.soft(page.getByRole('treeitem', { name: 'Test Building 1' }).locator('..').locator('..').locator('..').locator('..').locator('..')).toHaveScreenshot('Neo-searchresults-locationfilter.png');
    });

});

test('Check facilities filter', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters - Facilities'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Expand the facilities filter', async () => {
        if (projectViewport.width > 1023){
            await expect(page.getByLabel('Filter by')).toBeVisible();
            await expect(page.getByRole('button', { name: 'Facilities', exact:true})).toBeVisible();
            await page.getByRole('button', { name: 'Facilities', exact:true}).click();
        }
        else{
            await expect(page.getByRole('button', { name: 'All filters' })).toBeVisible();
            await page.getByRole('button', { name: 'All filters' }).click();
            await expect(page.getByRole('heading', { name: 'All filters' })).toBeVisible();   
            await expect(page.getByRole('button', { name: 'Facilities'})).toBeVisible();
            await page.getByRole('button', { name: 'Facilities'}).click();
        }            
    });

    await test.step('Check the expected listed facility filters are present and unchecked', async () => {
        await expect(page.getByRole('checkbox', { name: 'Adjustable Desk' })).toBeVisible();
        await expect(page.getByLabel('Facilities').getByText('Adjustable Desk')).toBeVisible();
        await expect(page.getByRole('checkbox', { name: 'Adjustable Desk' })).toBeChecked({checked:false});
        await expect(page.getByRole('checkbox', { name: 'Air Conditioning' })).toBeVisible();
        await expect(page.getByLabel('Facilities').getByText('Air Conditioning')).toBeVisible();
        await expect(page.getByRole('checkbox', { name: 'Air Conditioning' })).toBeChecked({checked:false});
        await expect(page.getByRole('checkbox', { name: 'Coffee Machine' })).toBeVisible();
        await expect(page.getByLabel('Facilities').getByText('Coffee Machine')).toBeVisible();
        await expect(page.getByRole('checkbox', { name: 'Coffee Machine' })).toBeChecked({checked:false});
        await expect(page.getByRole('checkbox', { name: 'Computer' })).toBeVisible();
        await expect(page.getByLabel('Facilities').getByText('Computer')).toBeVisible();
        await expect(page.getByRole('checkbox', { name: 'Computer' })).toBeChecked({checked:false});
    });
    
    await test.step('Compare open facility filter against golden file', async () => {
            await expect.soft(page.getByText('FacilitiesAdjustable DeskAir')).toHaveScreenshot('Neo-searchresults-facilityfilter.png');
    });
    
});

test('Check layouts filter', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters - Layouts'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Expand the Layouts filter', async () => {
        if (projectViewport.width > 1023){
            await expect(page.getByLabel('Filter by')).toBeVisible();
            await expect(page.getByRole('button', { name: 'Layouts', exact:true})).toBeVisible();
            await page.getByRole('button', { name: 'Layouts', exact:true}).click();
        }
        else{
            await expect(page.getByRole('button', { name: 'All filters' })).toBeVisible();
            await page.getByRole('button', { name: 'All filters' }).click();
            await expect(page.getByRole('heading', { name: 'All filters' })).toBeVisible();  
            await expect(page.getByRole('button', { name: 'Layouts'})).toBeVisible();
            await page.getByRole('button', { name: 'Layouts'}).click();
        }            
    });

    await test.step('Check the expected listed Layouts filters are present and unselected', async () => {
        await expect(page.getByLabel('Banquet')).toBeVisible();
        await expect(page.getByLabel('Banquet')).toBeChecked({checked:false});
        await expect(page.getByLabel('Buffet')).toBeVisible();
        await expect(page.getByLabel('Buffet')).toBeChecked({checked:false});
        await expect(page.getByLabel('Cabaret')).toBeVisible();
        await expect(page.getByLabel('Cabaret')).toBeChecked({checked:false});
    });
    
    await test.step('Compare open Layouts filter against golden file', async () => {
            await expect.soft(page.getByText('LayoutsBanquetBuffetCabaret')).toHaveScreenshot('Neo-searchresults-layoutsfilter.png');
    });
    
});

test('Set a location filter', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters - Location'}  }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking'); 
    });

    await test.step('Open the filter menu if in medium or small view', async () => {
        if (projectViewport.width < 1024){
            await expect(page.getByRole('button', { name: 'Filter' })).toBeVisible();
            await page.getByRole('button', { name: 'Filter' }).click();
            await expect(page.getByRole('heading', { name: 'Filter by' })).toBeVisible();    
        }            
    });

    await test.step('Expand the location filter & select Ground Floor', async () => {
        await page.getByRole('treeitem', { name: 'Test Building' }).locator('img').first().click();
        await expect(page.getByRole('treeitem', { name: 'Ground Floor' })).toHaveAttribute('aria-selected', 'false' );
        await page.getByRole('treeitem', { name: 'Ground Floor' }).click();
        await expect(page.getByRole('treeitem', { name: 'Ground Floor' })).toHaveAttribute('aria-selected', 'true' );
        if (projectViewport.width < 1024){
            await page.getByRole('button', { name: 'Apply' }).click();
        }   
    });
    
    await test.step('Check listed resource are as expected.', async () => {
        await expect(page.getByRole('status')).toContainText('2 meeting rooms available');
        await expect(page.locator('#main')).toContainText('Ground Floor Room 1');
        await expect(page.locator('#main')).toContainText('Ground Floor Room 2');
        await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeHidden();
    });
    
    await test.step('Compare results against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-searchresults-locationfilterselectedresults.png');
    });
    
});

test('Set a facilities filter', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters - Facilities'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Expand the Facilities filter', async () => {
        if (projectViewport.width > 1023){
            await expect(page.getByLabel('Filter by')).toBeVisible();
            await page.getByRole('button', { name: 'Facilities', exact:true}).click();
        }
        else{
            await page.getByRole('button', { name: 'All filters' }).click();
            await page.getByRole('button', { name: 'Facilities'}).click();
        }            
    });

    await test.step('Click the Air Conditioning option & compare selected filter against golden file', async () => {
        await page.getByRole('checkbox', { name: 'Air Conditioning' }).click();
        await expect(page.getByRole('checkbox', { name: 'Air Conditioning' })).toBeChecked();
        await expect.soft(page.getByText('FacilitiesAdjustable DeskAir')).toHaveScreenshot('Neo-searchresults-airconlayoutselected.png');
        if (projectViewport.width < 1024){
            await page.getByRole('button', { name: 'Apply' }).click();
            await expect(page.getByRole('checkbox', { name: 'Air Conditioning' })).toBeHidden();
        }
    });

    await test.step('Check listed resource are as expected.', async () => {
        await expect(page.getByRole('status')).toContainText('1 meeting room available');
        await expect(page.locator('#main')).toContainText('Basement Room 7 - Facilities');
        await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeHidden();
    });
    
    await test.step('Compare results against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-searchresults-facilitiesfilterselectedresults.png');
    });
    
});

test('Set a layout filter', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters - Layouts'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Expand the Layouts filter', async () => {
        if (projectViewport.width > 1023){
            await expect(page.getByLabel('Filter by')).toBeVisible();
            await page.getByRole('button', { name: 'Layouts', exact:true}).click();
        }
        else{
            await page.getByRole('button', { name: 'All filters' }).click();
            await page.getByRole('button', { name: 'Layouts'}).click();
        }            
    });

    await test.step('Click the Banquet layout & compare selected filter against golden file', async () => {
        await page.getByLabel('Banquet').click();
        await expect(page.getByLabel('Banquet')).toBeChecked();
        await expect.soft(page.getByText('LayoutsBanquetBuffetCabaret')).toHaveScreenshot('Neo-searchresults-banquetlayoutselected.png');
        if (projectViewport.width < 1024){
            await page.getByRole('button', { name: 'Apply' }).click();
            await expect(page.getByLabel('Banquet')).toBeHidden();
        }
    });

    await test.step('Check listed resource are as expected.', async () => {
        await expect(page.getByRole('status')).toContainText('1 meeting room available');
        await expect(page.locator('#main')).toContainText('Basement Room 8 - Layout');
        await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeHidden();
    });
    
    await test.step('Compare results against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-searchresults-layoutfilterselectedresults.png');
    });
    
});

test('Clear all filters', { tag: '@parallel', annotation:{type:'coa', description:'Search results screen - Filters - Clear all'} }, async ({ page, loginPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Expand the Facilities filter', async () => {
        if (projectViewport.width > 1023){
            await expect(page.getByLabel('Filter by')).toBeVisible();
            await page.getByRole('button', { name: 'Facilities', exact:true}).click();
        }
        else{
            await page.getByRole('button', { name: 'All filters' }).click();
            await page.getByRole('button', { name: 'Facilities'}).click();
        }            

        await expect(page.getByRole('button', { name: 'Clear all' })).toBeVisible();
    });

    await test.step('Click the Air Conditioning option', async () => {
        await page.getByRole('checkbox', { name: 'Air Conditioning' }).click();
        await expect(page.getByRole('checkbox', { name: 'Air Conditioning' })).toBeChecked();
    });

    await test.step('Click Clear All & validate', async () => {   
        if (projectViewport.width > 1023){
            await expect(page.getByRole('status')).toContainText('1 meeting room available');
            await expect(page.locator('#main')).toContainText('Basement Room 7 - Facilities');
            await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeHidden();
        }

        await page.getByRole('button', { name: 'Clear all' }).click();
        await expect(page.getByRole('checkbox', { name: 'Air Conditioning' })).toBeChecked({checked:false});
        
        if (projectViewport.width > 1023){
            await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeVisible();
        }
    });

    await test.step('Expand the Layouts filter', async () => {
        if (projectViewport.width > 1023){
            await expect(page.getByLabel('Filter by')).toBeVisible();
            await page.getByRole('button', { name: 'Layouts', exact:true}).click();
        }
        else{
            await page.getByRole('button', { name: 'Layouts'}).click();
        }    

        await expect(page.getByRole('button', { name: 'Clear all' })).toBeVisible(); 

    });

    await test.step('Click the Banquet layout option', async () => {
        await page.getByLabel('Banquet').click();
        await expect(page.getByLabel('Banquet')).toBeChecked();
    });
    
    await test.step('Click Clear All & validate', async () => {   
        if (projectViewport.width > 1023){
            await expect(page.getByRole('status')).toContainText('1 meeting room available');
            await expect(page.locator('#main')).toContainText('Basement Room 8 - Layout');
            await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeHidden();
        }

        await page.getByRole('button', { name: 'Clear all' }).click();
        await expect(page.getByLabel('Banquet')).toBeChecked({checked:false});
        
        if (projectViewport.width > 1023){
            await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeVisible();
        }
    });

    await test.step('Expand the location filter & select Ground Floor', async () => {

        if (projectViewport.width < 1024){
            await expect(page.getByRole('heading', { name: 'All filters' })).toBeVisible();    
        }

        await page.getByRole('treeitem', { name: 'Test Building' }).locator('img').first().click();
        await page.getByRole('treeitem', { name: 'Ground Floor' }).click();
        await expect(page.getByRole('treeitem', { name: 'Ground Floor' })).toHaveAttribute('aria-selected', 'true' );
    });
    
    await test.step('Click Clear All & validate', async () => {   
        if (projectViewport.width > 1023){
            await expect(page.getByRole('status')).toContainText('2 meeting rooms available');
            await expect(page.locator('#main')).toContainText('Ground Floor Room 1');
            await expect(page.locator('#main')).toContainText('Ground Floor Room 2');
            await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeHidden();
        }

        await page.getByRole('button', { name: 'Clear all' }).click();
        await expect(page.getByRole('treeitem', { name: 'Ground Floor' })).toHaveAttribute('aria-selected', 'false' );
        
        if (projectViewport.width > 1023){
            await expect(page.getByText('Basement Room 1Basement, Test Building 1Book Basement Room 1Book Basement Room')).toBeVisible();
        }
    });

});