//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Date - open and check elements', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Date'} }, async ({ page, loginPage }) => {
    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    let dateNow = dayjs();
    
    await test.step('Open calendar & check basic elements are presented', async () => {
        await page.locator(`[aria-label="Date ${dateNow.format('ddd, D MMM')} expandable"]:visible`).click();
        await expect(page.getByRole('button', { name: 'Today' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Tomorrow' })).toBeVisible();
        await expect(page.getByLabel('Monday')).toBeVisible();
        await expect(page.getByLabel('Tuesday')).toBeVisible();
        await expect(page.getByLabel('Wednesday')).toBeVisible();
        await expect(page.getByLabel('Thursday')).toBeVisible();
        await expect(page.getByLabel('Friday')).toBeVisible();
        await expect(page.getByLabel('Saturday')).toBeVisible();
        await expect(page.getByLabel('Sunday')).toBeVisible();

        //Elements checked below only apply to small view
        if (projectViewport.width < 768){
            await expect(page.getByRole('heading', { name: 'Select date' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Done' })).toBeVisible();
        }
    });

    await test.step('Attempt to check all expected days are listed', async () => {
        let currentDate = dateNow.startOf('month');
        const endDate = dateNow.endOf('month');
        let listOfDates = '';
        const calendarDatePanel = page.locator('[class="w-full border-collapse space-y-1"]')
        
        //Loop through all expected days in the months and add to a string
        while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
            listOfDates = listOfDates + " " + currentDate.format('D');
            currentDate = currentDate.add(1, 'day');
        }

        await expect(calendarDatePanel).toContainText(listOfDates);
    });

    await test.step('Compare against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-booking-search-openCalendar.png');
    });

});

test('Date - change months', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Date'} }, async ({ page, loginPage }) => {
    
    const standard_user_data = require('../test-data/neo/Users/<USER>');

    

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    let dateNow = dayjs();

    await test.step('Open calendar, check it is on current month & that I cannot go back a month', async () => {
        await page.locator(`[aria-label="Date ${dateNow.format('ddd, D MMM')} expandable"]:visible`).click();
        await expect(page.getByText(`${dateNow.format('MMMM YYYY')}`)).toBeVisible();
        await expect(page.getByLabel('Go to next month')).toBeVisible();
        await expect(page.getByLabel('Go to next month')).toBeEnabled();
        await expect(page.getByLabel('Go to previous month')).toBeVisible();
        await expect(page.getByLabel('Go to previous month')).toBeDisabled();  
    });
    
    await test.step('Navigate to next month & check back to previous month is available', async () => {
        await page.getByLabel('Go to next month').click();
        await expect(page.getByLabel('Go to next month')).toBeEnabled();
        await expect(page.getByLabel('Go to previous month')).toBeEnabled();
        await expect(page.getByText(`${dateNow.add(1,'month').format('MMMM YYYY')}`)).toBeVisible();
    });

    await test.step('Navigate one more month ahead', async () => {
        await page.getByLabel('Go to next month').click();
        await expect(page.getByLabel('Go to next month')).toBeEnabled();
        await expect(page.getByLabel('Go to previous month')).toBeEnabled();
        await expect(page.getByText(`${dateNow.add(2,'month').format('MMMM YYYY')}`)).toBeVisible();
    });

    await test.step('Navigate back to original month', async () => {
        await page.getByLabel('Go to previous month').click();
        await expect(page.getByLabel('Go to next month')).toBeEnabled();
        await expect(page.getByLabel('Go to previous month')).toBeEnabled();
        await expect(page.getByText(`${dateNow.add(1,'month').format('MMMM YYYY')}`)).toBeVisible();
        await page.getByLabel('Go to previous month').click();
        await expect(page.getByLabel('Go to next month')).toBeVisible();
        await expect(page.getByLabel('Go to previous month')).toBeDisabled();
        await expect(page.getByText(`${dateNow.format('MMMM YYYY')}`)).toBeVisible();
    });
    
});

test('Date - use today and tomorrow shortcuts', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Date'} }, async ({ page, loginPage }) => {
    const standard_user_data = require('../test-data/neo/Users/<USER>');

    

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    let dateNow = dayjs();
    
    await test.step('Open calendar click tomorrow', async () => {
        await page.locator(`[aria-label="Date ${dateNow.format('ddd, D MMM')} expandable"]:visible`).click();
        await expect(page.getByRole('button', { name: 'Tomorrow' })).toBeVisible();
        await page.getByRole('button', { name: 'Tomorrow' }).click();

        if (projectViewport.width < 768){

            const selectedCalendarButton = page.locator('[class="rdp-button_reset rdp-button focus-visible:border-white focus-visible:border ring-offset-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-denim inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50 hover:bg-bg-hover active:bg-bg-active h-9 w-9 p-0 aria-selected:opacity-100 rounded m-0.5 bg-denim hover:bg-denim active:bg-denim text-white"]')
            await expect(selectedCalendarButton).toBeVisible();
            await expect(selectedCalendarButton).toHaveText(dateNow.add(1, 'day').format('D'));
            await expect(selectedCalendarButton).toHaveAttribute('aria-selected', 'true');
            const color = await selectedCalendarButton.evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color")});
            expect(color).toBe("rgb(15, 101, 196)");

            await page.getByRole('button', { name: 'Done' }).click();
        }

        await expect(page.locator(`[aria-label="Date ${dateNow.add(1,'day').format('ddd, D MMM')} expandable"]:visible`)).toBeVisible();
    });

    await test.step('Re-open calendar and click today', async () => {
        await page.locator(`[aria-label="Date ${dateNow.add(1,'day').format('ddd, D MMM')} expandable"]:visible`).click();
        await expect(page.getByRole('button', { name: 'Today' })).toBeVisible();
        page.getByRole('button', { name: 'Today' }).click();

        if (projectViewport.width < 768){
            const selectedTodayCalendarButton = page.locator('[class="rdp-button_reset rdp-button focus-visible:border-white focus-visible:border ring-offset-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-denim inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50 hover:bg-bg-hover active:bg-bg-active h-9 w-9 p-0 aria-selected:opacity-100 rounded m-0.5 bg-denim hover:bg-denim active:bg-denim text-white underline decoration-2 underline-offset-[6px] text-denim"]')
            await expect(selectedTodayCalendarButton).toBeVisible();
            await expect(selectedTodayCalendarButton).toHaveText(dateNow.format('D'));
            await expect(selectedTodayCalendarButton).toHaveAttribute('aria-selected', 'true');
            const color = await selectedTodayCalendarButton.evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color")});
            expect(color).toBe("rgb(15, 101, 196)");

            await page.getByRole('button', { name: 'Done' }).click();
        }
       
        await expect(page.locator(`[aria-label="Date ${dateNow.format('ddd, D MMM')} expandable"]:visible`)).toBeVisible();
    })
    
});

test('Date - select new date', { tag: '@parallel', annotation:{type:'coa', description:'New booking search screen - Date'} }, async ({ page, loginPage }) => {
    const standard_user_data = require('../test-data/neo/Users/<USER>');

    

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    let dateNow = dayjs();
    
    await test.step('Open calendar & check todays is selected and under lined', async () => {
        await page.locator(`[aria-label="Date ${dateNow.format('ddd, D MMM')} expandable"]:visible`).click();
        await expect(page.getByText(`${dateNow.format('MMMM YYYY')}`)).toBeVisible();
        const selectedTodayCalendarButton = page.locator('[class="rdp-button_reset rdp-button focus-visible:border-white focus-visible:border ring-offset-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-denim inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50 hover:bg-bg-hover active:bg-bg-active h-9 w-9 p-0 aria-selected:opacity-100 rounded m-0.5 bg-denim hover:bg-denim active:bg-denim text-white underline decoration-2 underline-offset-[6px] text-denim"]');
        await expect(selectedTodayCalendarButton).toBeVisible();
        await expect(selectedTodayCalendarButton).toHaveText(dateNow.format('D'));
        await expect(selectedTodayCalendarButton).toHaveAttribute('aria-selected', 'true');
        await expect(selectedTodayCalendarButton).toHaveCSS('text-decoration-line', 'underline')
        const color = await selectedTodayCalendarButton.evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color")});
        expect(color).toBe("rgb(15, 101, 196)");  
    });

    await test.step('Check the day before is disabled & I cannot select it', async () => {
        //Please note: 
        //The messy logic below is an attempt to make sure yesterday's calendar button is disabled and when clicked nothing happens
        //This sounds easy but you can't rely on the button numbers as the calendar AND the dropdown UI can have the same attributes sometimes
        
        //Load all the buttons on the page in to a object
        const allButtons = page.locator('button');
        //Find the index of the selected today button (using the silly class name), with the object
        const selectedTodayCalendarButton = page.locator('[class="rdp-button_reset rdp-button focus-visible:border-white focus-visible:border ring-offset-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-denim inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50 hover:bg-bg-hover active:bg-bg-active h-9 w-9 p-0 aria-selected:opacity-100 rounded m-0.5 bg-denim hover:bg-denim active:bg-denim text-white underline decoration-2 underline-offset-[6px] text-denim"]');
        const targetButtonIndex = await allButtons.evaluateAll((elements, selectedTodayCalendarButton) => {
            return elements.findIndex(el => el === selectedTodayCalendarButton);
          }, await selectedTodayCalendarButton.elementHandle());
        //Create a locator for the previous date button by minusing 1 from the index
        const previousCalendarButton = allButtons.nth(targetButtonIndex - 1);
      
        await expect(previousCalendarButton).toBeVisible();
        await expect(previousCalendarButton).toHaveText(dateNow.subtract(1, 'day').format('D'));
        await expect(previousCalendarButton).toBeDisabled();
        await previousCalendarButton.click({force:true});
        await expect(selectedTodayCalendarButton).toHaveAttribute('aria-selected', 'true');
    });
    
    let selectedDate = dayjs();

    await test.step('Select another day (the 15th of next month to avoid issues)', async () => {
        await page.getByLabel('Go to next month').click();
        await page.getByRole('button', { name: '15', exact: true }).click();
        
        
        if (projectViewport.width < 768){
            
            await page.getByRole('button', { name: 'Done' }).click();
        }

        selectedDate = dateNow.add(1,'month').date(15);
        await expect(page.locator(`[aria-label="Date ${selectedDate.format('ddd, D MMM')} expandable"]:visible`)).toBeVisible();
    });

    await test.step('Re-open date picker and ensure selected date is marked as selected', async () => {
        await page.locator(`[aria-label="Date ${selectedDate.format('ddd, D MMM')} expandable"]:visible`).click();
        await expect(page.getByRole('button', { name: '15', exact: true })).toBeVisible();
        await expect(page.getByRole('button', { name: '15', exact: true })).toHaveAttribute('aria-selected', 'true');
        const color = await page.getByRole('button', { name: '15', exact: true }).evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color")});
        expect(color).toBe("rgb(15, 101, 196)");
    })
    
    await test.step('Ensure today is indicated underlined even when not selected', async () => {
        await page.getByLabel('Go to previous month').click();
        const unselectedTodayCalendarButton = page.locator('[class="rdp-button_reset rdp-button focus-visible:border-white focus-visible:border ring-offset-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-denim inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50 hover:bg-bg-hover active:bg-bg-active h-9 w-9 p-0 aria-selected:opacity-100 rounded m-0.5 underline decoration-2 underline-offset-[6px] text-denim"]');
        await expect(unselectedTodayCalendarButton).toBeVisible();
        await expect(unselectedTodayCalendarButton).toHaveText(dateNow.format('D'));
        await expect(unselectedTodayCalendarButton).toHaveCSS('text-decoration-line', 'underline')
        const color = await unselectedTodayCalendarButton.evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color")});
        expect(color).toBe("rgba(0, 0, 0, 0)");
    });
        
});