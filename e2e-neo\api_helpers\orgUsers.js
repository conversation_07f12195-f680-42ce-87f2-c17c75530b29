import { request } from '@playwright/test';
import dayjs from 'dayjs';

export async function createStandardUser(request, baseURL) {

    const dateTimeNow = dayjs();
    const longTime = dateTimeNow.format('DDMMHHmmssSSS');

    const userEmail = `user${longTime}@test${longTime}.com`;

    const admin = require('../../test-data/neo/Users/<USER>');

    const loginAdmin = await request.post(`${baseURL}api/v1/user/login`, {
        data: {
            username: admin.Email,
            password: admin.Password,
            rememberMe: true
        }
    });

    const userDetails = ({
        "Email": userEmail,
        "FirstName":"Firstname",
        "LastName":"Lastname",
        "Name":"Firstname Lastname",
        "Password":"gIdajl@&w$Qa6r"
    });

    const createUser = await request.post(`${baseURL}api/v1/user`,{
        data: {
            email:userDetails.Email,
            firstName:userDetails.FirstName,
            lastName:userDetails.LastName,
            vehicleType:"CAR",
            password:"gIdajl@&w$Qa6r",
            roles:["USER"],
            locationRoles:[]
        },
        timeout: 30000
    });

    return (userDetails)

}