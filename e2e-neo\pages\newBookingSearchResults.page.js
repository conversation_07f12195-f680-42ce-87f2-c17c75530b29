import dayjs from 'dayjs';

const timeNow = dayjs();

/**
 * Page Object Model for the search results page
 * @class
 * @param {import ('@playwright/test').Page} page 
 */
export class NewBookingSearchResultsPage {

  constructor(page) {
    this.page = page;
    
    // Search page elements
    this.mainHeading = page.getByRole('heading', { name: 'New booking' });
    this.resourceTypeField = page.getByRole('button', { name: 'Resource type' });
    
    // Resource type options
    this.meetingRoomOption = page.locator('span:text("Meeting Room"):visible');
    this.deskOption = page.locator('span:text("Desks"):visible');
    this.officesOption = page.locator('span:text("Offices"):visible');
    this.bedroomsOption = page.locator('span:text("Bedrooms"):visible');

    this.dateField = page.locator(`[aria-label="Date ${timeNow.format('ddd, D MMM')} expandable"]:visible`);
    this.locationField = page.locator(`[aria-label="Location Wales expandable"]:visible`);
    this.startTimeField = page.getByRole('combobox', { name: 'Start time' });
    this.endTimeField = page.getByRole('combobox', { name: 'End time' });
    
    this.capacityField = page.getByRole('button', { name: 'Capacity' });
    // Capacity options
    this.capacityAnyOption = page.locator('span:text("Any"):visible').nth(1);
    this.capacity1Option = page.locator('span:text("1 or more"):visible');
    this.capacity5Option = page.locator('span:text("5 or more"):visible');
    this.capacity10Option = page.locator('span:text("10 or more"):visible');
    this.capacity20Option = page.locator('span:text("20 or more"):visible');
    this.capacity50Option = page.locator('span:text("50 or more"):visible');

    this.searchButton = page.getByRole('button', { name: 'Search' });

    // Page elements
    this.pageTitle = page.getByRole('heading', { name: 'Search results' });
    this.resultsCount = page.getByText(/Showing [0-9]+ results/);
    
    // Filter elements
    this.filterButton = page.getByRole('button', { name: 'Filter' });
    this.dateFilterButton = page.getByRole('button', { name: 'Date' });
    this.timeFilterButton = page.getByRole('button', { name: 'Time' });
    this.locationFilterButton = page.getByRole('button', { name: 'Location' });
    this.resourceTypeFilterButton = page.getByRole('button', { name: 'Resource type' });
    this.capacityFilterButton = page.getByRole('button', { name: 'Capacity' });
    this.featuresFilterButton = page.getByRole('button', { name: 'Features' });
    
    // Resource actions
    this.bookResourceButton = (resourceName) => page.getByRole('button', { name: `Book ${resourceName}` });
    this.viewResourceButton = (resourceName) => page.getByRole('button', { name: `View ${resourceName}` });
    
    // Resource information
    this.resourceCard = (resourceName) => page.getByRole('heading', { name: resourceName }).locator('..');
    this.resourceAvailability = (resourceName) => this.resourceCard(resourceName).getByText(/Available|Unavailable/);
    this.resourceCapacity = (resourceName) => this.resourceCard(resourceName).getByText(/Capacity: [0-9]+/);
    this.resourceLocation = (resourceName) => this.resourceCard(resourceName).getByText(/Location:/);
    
    // Sorting and view options
    this.sortButton = page.getByRole('button', { name: 'Sort' });
    this.viewAsListButton = page.getByRole('button', { name: 'View as list' });
    this.viewAsGridButton = page.getByRole('button', { name: 'View as grid' });
    
    // Pagination
    this.paginationNextButton = page.getByRole('button', { name: 'Next page' });
    this.paginationPreviousButton = page.getByRole('button', { name: 'Previous page' });
    this.paginationPageButtons = page.getByRole('button').filter({ hasText: /^[0-9]+$/ });
  }

  /**
   * Navigate to the search results page
   */
  async goto() {
    await this.page.goto('/neo/search-results');
  }

  /**
   * Book a specific resource
   * @param {string} resourceName - The name of the resource to book
   */
  async bookResource(resourceName) {
    // Find the specific resource card by its header first
    const resourceSection = await this.page
      .getByRole('heading', { name: resourceName, exact: true })
      .locator('..').locator('..').locator('..').locator('..');
    
    // Then find and click the Book button within that section
    await resourceSection
      .getByRole('button', { name: `Book`})
      .click();
  }

  /**
   * View details of a specific resource
   * @param {string} resourceName - The name of the resource to view
   */
  async viewResource(resourceName) {
    await this.viewResourceButton(resourceName).click();
  }

  /**
   * Check if a specific resource is available
   * @param {string} resourceName - The name of the resource to check
   * @returns {Promise<boolean>} - Whether the resource is available
   */
  async isResourceAvailable(resourceName) {
    const availabilityText = await this.resourceAvailability(resourceName).textContent();
    return availabilityText.includes('Available');
  }

  /**
   * Get the capacity of a specific resource
   * @param {string} resourceName - The name of the resource
   * @returns {Promise<number>} - The capacity of the resource
   */
  async getResourceCapacity(resourceName) {
    const capacityText = await this.resourceCapacity(resourceName).textContent();
    const match = capacityText.match(/Capacity: ([0-9]+)/);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * Get the location of a specific resource
   * @param {string} resourceName - The name of the resource
   * @returns {Promise<string>} - The location of the resource
   */
  async getResourceLocation(resourceName) {
    const locationText = await this.resourceLocation(resourceName).textContent();
    return locationText.replace('Location:', '').trim();
  }

  /**
   * Apply date filter
   * @param {string} date - The date to filter by (format: 'DD/MM/YYYY')
   */
  async filterByDate(date) {
    await this.dateFilterButton.click();
    await this.page.getByLabel('Date').fill(date);
    await this.page.keyboard.press('Enter');
    await this.page.getByRole('button', { name: 'Apply' }).click();
  }

  /**
   * Apply time filter
   * @param {string} startTime - The start time to filter by (format: 'h:mm A')
   * @param {string} endTime - The end time to filter by (format: 'h:mm A')
   */
  async filterByTime(startTime, endTime) {
    await this.timeFilterButton.click();
    await this.page.getByLabel('Start time').fill(startTime);
    await this.page.getByLabel('End time').fill(endTime);
    await this.page.getByRole('button', { name: 'Apply' }).click();
  }

  /**
   * Apply location filter
   * @param {string} location - The location to filter by
   */
  async filterByLocation(location) {
    await this.locationFilterButton.click();
    await this.page.getByText(location, { exact: true }).click();
    await this.page.getByRole('button', { name: 'Apply' }).click();
  }

  /**
   * Apply resource type filter
   * @param {string} resourceType - The resource type to filter by (e.g., 'Meeting Room', 'Desk')
   */
  async filterByResourceType(resourceType) {
    await this.resourceTypeFilterButton.click();
    await this.page.getByText(resourceType, { exact: true }).click();
    await this.page.getByRole('button', { name: 'Apply' }).click();
  }

  /**
   * Apply capacity filter
   * @param {string} capacity - The capacity to filter by (e.g., '1 or more', '5 or more')
   */
  async filterByCapacity(capacity) {
    await this.capacityFilterButton.click();
    await this.page.getByText(capacity, { exact: true }).click();
    await this.page.getByRole('button', { name: 'Apply' }).click();
  }

  /**
   * Sort results
   * @param {string} sortOption - The sort option to apply (e.g., 'Name A-Z', 'Capacity high-low')
   */
  async sortResults(sortOption) {
    await this.sortButton.click();
    await this.page.getByText(sortOption, { exact: true }).click();
  }

  /**
   * Change view mode
   * @param {string} viewMode - The view mode to apply ('list' or 'grid')
   */
  async changeViewMode(viewMode) {
    if (viewMode.toLowerCase() === 'list') {
      await this.viewAsListButton.click();
    } else if (viewMode.toLowerCase() === 'grid') {
      await this.viewAsGridButton.click();
    }
  }

  /**
   * Navigate to a specific page of results
   * @param {number} pageNumber - The page number to navigate to
   */
  async goToPage(pageNumber) {
    await this.paginationPageButtons.filter({ hasText: String(pageNumber) }).click();
  }

  /**
   * Go to the next page of results
   */
  async goToNextPage() {
    await this.paginationNextButton.click();
  }

  /**
   * Go to the previous page of results
   */
  async goToPreviousPage() {
    await this.paginationPreviousButton.click();
  }

  /**
   * Get the total number of results
   * @returns {Promise<number>} - The total number of results
   */
  async getResultsCount() {
    const countText = await this.resultsCount.textContent();
    const match = countText.match(/Showing ([0-9]+) results/);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * Verify a specific resource is displayed in the results
   * @param {string} resourceName - The name of the resource to verify
   */
  async verifyResourceDisplayed(resourceName) {
    await this.resourceCard(resourceName).waitFor({ state: 'visible' });
  }
}
