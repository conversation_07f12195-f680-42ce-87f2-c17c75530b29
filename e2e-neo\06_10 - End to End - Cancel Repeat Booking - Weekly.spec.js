import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to End - Cancel repeat booking - Weekly', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Cancel repeat booking - Weekly' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login and create a weekly repeat booking to cancel', async () => {
        await loginPage.simpleLogin(email_user_data);

        // Create initial repeat booking for testing cancellation
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const repeatUntilDate = dayjs(bookingFromTime).add(4, 'week');
        const bookingTitle = `Cancel Weekly Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm')}`;

        await page.getByRole('button', { name: 'Search' }).click();
        await searchResultsPage.bookResource('Basement Room 2');
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
        await newBookingModalPage.setupRepeatBooking('Weekly', repeatUntilDate, projectViewport.width);
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.completeBooking();
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    await test.step('Verify cancel dialog options for weekly repeat booking', async () => {
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Cancel Weekly Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm')}`;

        await myBookingsPage.openCancelDialog(bookingFromTime, bookingTitle, projectViewport.width);

        await expect(myBookingsPage.cancelDialogTitle).toBeVisible();
        await expect(myBookingsPage.cancelDialogMessage).toHaveText('This action cannot be undone and will make the room available for others to book.');
        await expect(myBookingsPage.cancelSingleButton).toHaveText('Cancel just this booking');
        await expect(myBookingsPage.cancelAllButton).toHaveText('Cancel all remaining bookings in this series');
        await expect(myBookingsPage.cancelDialogCloseButton).toBeVisible();
        await expect(myBookingsPage.confirmCancelButton).toBeVisible();

        // Verify radio button states
        await expect(myBookingsPage.cancelSingleButton).toHaveClass(/cursor-not-allowed/);
        await expect(myBookingsPage.cancelSingleButton).toHaveClass(/bg-border-decoration/);
        await expect(myBookingsPage.cancelSingleButton).toBeDisabled();
        
        await expect(myBookingsPage.cancelAllButton).not.toHaveClass(/cursor-not-allowed/);
        await expect(myBookingsPage.cancelAllButton).not.toBeDisabled();

        await expect.soft(page).toHaveScreenshot('Neo-cancel-weekly-dialog.png');

        await myBookingsPage.cancelDialogCloseButton.click();
        if (projectViewport.width < 768) {
            await page.getByRole('button', { name: 'close' }).click();
        }
        await myBookingsPage.verifyBookingExists(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
    });

    await test.step('Cancel single occurrence of weekly repeat booking', async () => {
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Cancel Weekly Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm')}`;
        const nextWeekBooking = dayjs(bookingFromTime).add(1, 'week');

        await myBookingsPage.cancelBooking(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            false,
            projectViewport.width
        );

        await myBookingsPage.verifyBookingExists(
            nextWeekBooking,
            dayjs(nextWeekBooking).add(30, 'minutes'),
            bookingTitle,
            projectViewport.width
        );

        await expect.soft(page).toHaveScreenshot('Neo-cancel-weekly-single.png');
    });

    await test.step('Cancel all remaining occurrences', async () => {
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Cancel Weekly Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm')}`;
        const nextWeekBooking = dayjs(bookingFromTime).add(1, 'week');

        await myBookingsPage.cancelBooking(
            nextWeekBooking,
            bookingToTime,
            bookingTitle,
            true,
            projectViewport.width
        );

        await myBookingsPage.verifyBookingDoesNotExist(
            nextWeekBooking,
            bookingTitle,
            projectViewport.width
        );

        const futureWeekBooking = dayjs(bookingFromTime).add(3, 'week');
        await myBookingsPage.verifyBookingDoesNotExist(
            futureWeekBooking,
            bookingTitle,
            projectViewport.width
        );

        await expect.soft(page).toHaveScreenshot('Neo-cancel-weekly-all.png');
    });
});