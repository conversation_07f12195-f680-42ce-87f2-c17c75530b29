// @ts-check
const { defineConfig, devices } = require('@playwright/test');
const dayjs = require('dayjs');
const neoActionTimeout = 10000;
const neoExpectTimeout = 10000;
const neoTestTimeout = 30000;
const neoPixelRatio = 0.07;

// Generate test run timestamp for monocart report naming
const testRunName = `Test-Run-${dayjs().format('DD-MM-YYYY-HH-mm-ss')}`;

// Configure workers logic
const DEFAULT_WORKERS = 1;
const workersArg = parseInt(
  process.argv.find(arg => arg.startsWith('--workers='))?.split('=')[1] ?? `${DEFAULT_WORKERS}`,
  10
);

if (workersArg !== 1) {
  process.env.PLAYWRIGHT_WORKERS = workersArg.toString();
}

/**
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 1 : 0,
  workers: workersArg,
  timeout: 120000,

  reporter: [
    ['list'],
    ['monocart-reporter', {
      name: `Test-Report-${testRunName}`,
      outputFile: `./monocart-report/${testRunName}/Test-Report-${testRunName}.html`
    }]
  ],

  use: {
    video: { mode: 'on' },
    screenshot: 'only-on-failure',
    baseURL: 'https://int.matrixbooking.com/',
    trace: 'retain-on-failure',

    launchOptions: {
      // slowMo: 100,
      ignoreDefaultArgs: ['--hide-scrollbars']
    }
  },

  expect: {
    timeout: 15000,
    toMatchSnapshot: { maxDiffPixelRatio: 0.1 },
    toHaveScreenshot: { maxDiffPixelRatio: 0.1 }
  },

  reportSlowTests: {
    max: 0,
    threshold: 60001
  },

  projects: [
    // Setup projects
    {
      name: 'setup',
      testDir: './e2e',
      testMatch: /.*\.setup\.js/
    },
    {
      name: 'setup-fpkiosk',
      testDir: './e2e-fpkiosk',
      testMatch: /.*\.setup\.js/
    },
    {
      name: 'setup-go',
      testDir: './e2e-go',
      testMatch: /.*\.setup\.js/
    },
    {
      name: 'setup-signage',
      testDir: './e2e-signage',
      testMatch: /.*\.setup\.js/
    },
    {
      name: 'setup-neo',
      testDir: './e2e-neo',
      testMatch: /.*\.setup\.js/,
      use: { actionTimeout: 5000 },
      timeout: 180000
    },

    // Main test configurations
    {
      name: 'debug',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1500, height: 800 }
      }
    },
    {
      name: 'e2e',
      dependencies: ['setup'],
      testDir: './e2e',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1500, height: 800 }
      },
      timeout: 140000
    },

    // Browser variations
    {
      name: 'e2e-firefox',
      dependencies: ['setup'],
      testDir: './e2e',
      use: {
        ...devices['Desktop Firefox'],
        viewport: { width: 1500, height: 800 }
      }
    },
    {
      name: 'e2e-safari',
      dependencies: ['setup'],
      testDir: './e2e',
      use: {
        ...devices['Desktop Safari'],
        viewport: { width: 1500, height: 800 }
      }
    },

    // Specialized test suites
    {
      name: 'e2e-go',
      testDir: './e2e-go',
      dependencies: ['setup-go'],
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1500, height: 800 }
      }
    },
    {
      name: 'e2e-fpkiosk',
      testDir: './e2e-fpkiosk',
      dependencies: ['setup-fpkiosk'],
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1500, height: 800 }
      }
    },
    {
      name: 'e2e-signage',
      testDir: './e2e-signage',
      dependencies: ['setup-signage'],
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1500, height: 800 }
      }
    },

    // NEO test variations
    {
      name: 'e2e-neo',
      dependencies: ['setup-neo'],
      testDir: './e2e-neo',
      snapshotDir: './e2e-neo/snapshots',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1500, height: 800 },
        video: {
          mode: 'on',
          size: { width: 1500, height: 800 },
        },
        actionTimeout: neoActionTimeout,
      },
      timeout: neoTestTimeout,
      expect: {
        timeout: neoExpectTimeout,
        toHaveScreenshot: { maxDiffPixelRatio: neoPixelRatio }
      }
    },
    {
      name: 'e2e-neo-medium',
      dependencies: ['setup-neo'],
      testDir: './e2e-neo',
      snapshotDir: './e2e-neo/snapshots',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 800, height: 800 },
        video: {
          mode: 'on',
          size: { width: 800, height: 800 },
        },
        actionTimeout: neoActionTimeout,
      },
      timeout: neoTestTimeout,
      expect: {
        timeout: neoExpectTimeout,
        toHaveScreenshot: { maxDiffPixelRatio: neoPixelRatio }
      }
    },
    {
      name: 'e2e-neo-small',
      dependencies: ['setup-neo'],
      testDir: './e2e-neo',
      snapshotDir: './e2e-neo/snapshots',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 400, height: 800 },
        video: {
          mode: 'on',
          size: { width: 400, height: 800 },
        },
        actionTimeout: neoActionTimeout,
      },
      timeout: neoTestTimeout,
      expect: {
        timeout: neoExpectTimeout,
        toHaveScreenshot: { maxDiffPixelRatio: neoPixelRatio }
      }
    },

    // Experimental tests
    {
      name: 'exp',
      testDir: './Experiments',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1500, height: 800 }
      }
    }
  ]
});