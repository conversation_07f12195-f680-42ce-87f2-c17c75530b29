//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check equipment is presented where configured', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 1', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

     await test.step('Validate that NO equipment is displayed as it is not congiured for this resource', async () => { 
        await expect(page.getByRole('heading', { name: 'Options' })).toBeHidden();
        await expect(newBookingModalPage.equipmentButton).toBeHidden();
        await page.getByRole('button', { name: 'Close panel' }).click();
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });
    
    await test.step('Validate that equipment is displayed for a resource where it is configured', async () => { 
        await expect(page.getByRole('heading', { name: 'Options' })).toBeVisible();
        await expect(newBookingModalPage.equipmentButton).toBeVisible();
    });
 
});

test('Open the equipment accordian and click add, then remove', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });
    
    await test.step('Click the option to expand its accordian', async () => {
        await newBookingModalPage.equipmentButton.click();
    });

    await test.step('Check the expected elements are presented', async () => {
        await expect(page.getByLabel('Equipment', {exact:true})).toBeVisible();
        await expect(newBookingModalPage.equipmentButton).toHaveAttribute('aria-expanded', 'true');
        await expect(page.getByRole('button', { name: 'Add equipment Add equipment' })).toBeVisible();
        await expect(page.getByLabel('Equipment').locator('span')).toContainText('No equipment added yet');
    });
    
    await test.step('Compare empty option against a golden file image', async () => {
        await expect.soft(page.getByLabel('Equipment')).toHaveScreenshot('Neo-bookingModal-EquipmentEmpty.png');
    });

    await test.step('Click Add option button and validate elements seen', async () => { 
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click(); 
        await expect(page.getByText('Equipment item 1Choose')).toBeVisible();
        await expect(page.locator('div').filter({ hasText: /^Equipment item 1$/ })).toBeVisible();
        await expect(page.getByLabel('remove', { exact: true })).toBeVisible();
        
        if (projectViewport.width>768){
            await expect(page.getByText('Option * Select equipment').first()).toBeVisible();
        } else {
            await expect(page.getByText('Option * Select equipment').nth(1)).toBeVisible();
        }
        
        await expect(page.getByLabel('Equipment (1)').getByText('Notes')).toBeVisible();
        await expect(page.getByLabel('Equipment (1)').getByLabel('Notes')).toBeVisible();
    });

    await test.step('Compare empty option against a golden file image', async () => {
        await expect.soft(page.getByText('Equipment item 1Choose')).toHaveScreenshot('Neo-bookingModal-EquipmentAdded.png');
    });

    await test.step('Click the remove button and validate elements', async () => {
        await page.getByLabel('remove', { exact: true }).click();
        await expect(page.getByLabel('Equipment'), {exact:true}).toBeVisible();
        await expect(page.getByLabel('Equipment').locator('span')).toContainText('No equipment added yet');
        await expect(page.getByText('Option * Select equipment').first()).toBeHidden();
        await expect(page.getByLabel('remove', { exact: true })).toBeHidden();
        await expect(page.getByLabel('Equipment (1)').getByText('Notes')).toBeHidden();
        await expect(page.getByLabel('Equipment (1)').getByLabel('Notes')).toBeHidden();
    });
       
});

test('Check equipment option with Time set to start time only type', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment - Time'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    const timeNow = dayjs();
    const startTime = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');
    const newStartTime = startTime.add(30, 'minute');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    await test.step('Click the option to expand its accordian & click add', async () => {
        await newBookingModalPage.equipmentButton.click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
    });

    await test.step('Click select option & select the option with time set to start only', async () => { 
        
        if (projectViewport.width>768){
            await page.getByText('Option * Select equipment').first().click();
            await page.getByRole('option', { name: 'Equipment option 1' }).click();
        } else {
            await page.getByText('Option * Select equipment').nth(1).click();
            await page.getByRole('button', { name: 'Equipment option 1' }).click();
        }
        
        await expect(page.getByLabel('Equipment (1)').getByText('Start time')).toBeVisible();
        await expect(page.getByLabel('Equipment (1)').getByText('End time')).toBeHidden();
        await expect(page.getByLabel('Equipment (1)').getByLabel('start-time')).toHaveValue(startTime.format('h:mm A'));
    });

    await test.step('Compare added option against a golden file image', async () => {
        await expect.soft(page.getByText('Equipment item 1Choose')).toHaveScreenshot('Neo-bookingModal-EquipmentStartTimeOnly.png');
    });

    await test.step('Change the time(s) by typing', async () => {
        await page.getByLabel('Equipment (1)').getByLabel('start-time').click();
        await page.getByLabel('Equipment (1)').getByLabel('start-time').fill('22');
        await page.getByLabel('Equipment (1)').getByLabel('start-time').press('Tab');
        await expect(page.getByLabel('Equipment (1)').getByLabel('start-time')).toHaveValue('10:00 PM');
    });

    await test.step('Change the time(s) by selecting from drop down', async () => {
        await page.getByLabel('Equipment (1)').getByLabel('Select a time').click();
        await page.getByRole('option', { name: newStartTime.format('h:mm A') }).click();
        await expect(page.getByLabel('Equipment (1)').getByLabel('start-time')).toHaveValue(newStartTime.format('h:mm A'));
    });
       
});

test('Check equipment option with Time set to start time & end time', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment - Time'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    const timeNow = dayjs();
    const startTime = timeNow.add(30 - (timeNow.minute() % 30), 'minute').startOf('minute');
    const endTime = startTime.add(1, 'hour');
    const newStartTime = startTime.add(15, 'minute');
    const newEndTime = endTime.subtract(15, 'minute');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    await test.step('Click the option to expand its accordian & click add', async () => {
        await newBookingModalPage.equipmentButton.click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
    });

    await test.step('Click select option & select the option with time set to start only', async () => { 
        
        if (projectViewport.width>768){
            await page.getByText('Option * Select equipment').first().click();
            await page.getByRole('option', { name: 'Equipment option 2' }).click();
        } else {
            await page.getByText('Option * Select equipment').nth(1).click();
            await page.getByRole('button', { name: 'Equipment option 2' }).click();
        }
        
        await expect(page.getByLabel('Equipment (1)').getByText('Start time')).toBeVisible();
        await expect(page.getByLabel('Equipment (1)').getByLabel('start-time')).toHaveValue(startTime.format('h:mm A'));
        await expect(page.getByLabel('Equipment (1)').getByText('End time')).toBeVisible();
        await expect(page.getByLabel('Equipment (1)').getByLabel('end-time')).toHaveValue(endTime.format('h:mm A'));
    });

    await test.step('Compare added option against a golden file image', async () => {
        await expect.soft(page.getByText('Equipment item 1Choose')).toHaveScreenshot('Neo-bookingModal-EquipmentStartAndEndTime.png');
    });

    await test.step('Change the time(s) by typing', async () => {
        await page.getByLabel('Equipment (1)').getByLabel('start-time').click();
        await page.getByLabel('Equipment (1)').getByLabel('start-time').fill('22');
        await page.getByLabel('Equipment (1)').getByLabel('start-time').press('Tab');
        await expect(page.getByLabel('Equipment (1)').getByLabel('start-time')).toHaveValue('10:00 PM');
        await page.getByLabel('Equipment (1)').getByLabel('end-time').click();
        await page.getByLabel('Equipment (1)').getByLabel('end-time').fill('23');
        await page.getByLabel('Equipment (1)').getByLabel('end-time').press('Tab');
        await expect(page.getByLabel('Equipment (1)').getByLabel('end-time')).toHaveValue('11:00 PM');
    });

    await test.step('Change the time(s) by selecting from drop down', async () => {
        await page.getByLabel('Equipment (1)').getByLabel('Select a time').first().click();
        await page.getByRole('option', { name: newStartTime.format('h:mm A') }).click();
        await expect(page.getByLabel('Equipment (1)').getByLabel('start-time')).toHaveValue(newStartTime.format('h:mm A'));
        await page.getByLabel('Equipment (1)').getByLabel('Select a time').nth(1).click();
        await page.getByRole('option', { name: newEndTime.format('h:mm A') }).click();
        await expect(page.getByLabel('Equipment (1)').getByLabel('end-time')).toHaveValue(newEndTime.format('h:mm A'));
    });
       
});

test('Check equipment option with Time set to duration', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment - Time'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    await test.step('Click the option to expand its accordian & click add', async () => {
        await newBookingModalPage.equipmentButton.click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
    });

    await test.step('Click select option & select the option with time set to start only', async () => { 
        
        if (projectViewport.width>768){
            await page.getByText('Option * Select equipment').first().click();
            await page.getByRole('option', { name: 'Equipment option 3' }).click();
        } else {
            await page.getByText('Option * Select equipment').nth(1).click();
            await page.getByRole('button', { name: 'Equipment option 3' }).click();
        }
        
        await expect(page.getByLabel('Equipment (1)').getByText('Start time')).toBeHidden();
        await expect(page.getByLabel('Equipment (1)').getByText('End time')).toBeHidden();
    });

    await test.step('Compare added option against a golden file image', async () => {
        await expect.soft(page.getByText('Equipment item 1Choose')).toHaveScreenshot('Neo-bookingModal-EquipmentDurationTime.png');
    });
       
});

test('Check equipment option with QTY set to item based', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment - Quantity'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    await test.step('Click the option to expand its accordian & click add', async () => {
        await newBookingModalPage.equipmentButton.click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
    });

    await test.step('Click select option & select the option with time set to start only', async () => { 
        
        if (projectViewport.width>768){
            await page.getByText('Option * Select equipment').first().click();
            await page.getByRole('option', { name: 'Equipment option 1' }).click();
        } else {
            await page.getByText('Option * Select equipment').nth(1).click();
            await page.getByRole('button', { name: 'Equipment option 1' }).click();
        }
        
        await expect(page.getByText('Quantity *')).toBeVisible();
        await expect(page.getByLabel('Quantity *')).toBeVisible();
        await expect(page.getByLabel('Quantity *')).toHaveValue('1');
    });

    await test.step('Compare added option against a golden file image', async () => {
        await expect.soft(page.getByText('Equipment item 1Choose')).toHaveScreenshot('Neo-bookingModal-EquipmentQtyItemBased.png');
    });

    await test.step('Change the value by typing', async () => {
       await page.getByLabel('Quantity *').click();
       await page.getByLabel('Quantity *').click();
       await page.getByLabel('Quantity *').fill('99');
       await expect(page.getByLabel('Quantity *')).toHaveValue('99');
    });
       
});

test('Check equipment option with QTY set to attendee based', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment - Quantity'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    await test.step('Add a internal attendee', async () => {
            
        if (projectViewport.width < 768){
            await newBookingModalPage.mobileAddAttendeesButton.click();
            await newBookingModalPage.mobileSearchField.fill(email_user_data.FirstName);
            await page.getByRole('option', {name: email_user_data.Name}).click();
            await page.getByRole('button', { name: 'Done' }).click();
        } else {
            await page.getByPlaceholder('Search by name or email').fill(email_user_data.FirstName);
            await page.getByRole('option', {name: email_user_data.Name}).click();
        }
               
    });

    await test.step('Click the option to expand its accordian & click add', async () => {
        await newBookingModalPage.equipmentButton.click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
    });

    await test.step('Click select option & select the option with time set to start only', async () => { 
        
        if (projectViewport.width>768){
            await page.getByText('Option * Select equipment').first().click();
            await page.getByRole('option', { name: 'Equipment option 3' }).click();
        } else {
            await page.getByText('Option * Select equipment').nth(1).click();
            await page.getByRole('button', { name: 'Equipment option 3' }).click();
        }
        
        await expect(page.getByText('Quantity *')).toBeVisible();
        await expect(page.getByLabel('Quantity *')).toBeVisible();
        await expect(page.getByLabel('Quantity *')).toHaveValue('2');
    });

    await test.step('Compare added option against a golden file image', async () => {
        await expect.soft(page.getByText('Equipment item 1Choose')).toHaveScreenshot('Neo-bookingModal-EquipmentQtyAttendeeBased.png');
    });

    await test.step('Change the value by typing', async () => {
       await page.getByLabel('Quantity *').click();
       await page.getByLabel('Quantity *').fill('99');
       await expect(page.getByLabel('Quantity *')).toHaveValue('99');
    });
       
});

test('Check equipment option with no QTY', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Equipment - Quantity'} }, async ({ page, loginPage, newBookingModalPage}) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 2', async () => {
       await expect(page.getByRole('button', { name: 'Book Basement Room 2', exact: true })).toBeVisible();
       await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    await test.step('Click the option to expand its accordian & click add', async () => {
        await newBookingModalPage.equipmentButton.click();
        await page.getByRole('button', { name: 'Add equipment Add equipment' }).click();
    });

    await test.step('Click select option & select the option with time set to start only', async () => { 
        
        if (projectViewport.width>768){
            await page.getByText('Option * Select equipment').first().click();
            await page.getByRole('option', { name: 'Equipment option 2' }).click();
        } else {
            await page.getByText('Option * Select equipment').nth(1).click();
            await page.getByRole('button', { name: 'Equipment option 2' }).click();
        }
        
        await expect(page.getByText('Quantity *')).toBeHidden();
        await expect(page.getByLabel('Quantity *')).toBeHidden();
    });

    await test.step('Compare added option against a golden file image', async () => {
        await expect.soft(page.getByText('Equipment item 1Choose')).toHaveScreenshot('Neo-bookingModal-EquipmentNoQty.png');
    });
       
});