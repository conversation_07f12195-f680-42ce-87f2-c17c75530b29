//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check JPG image displays on booking modal', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Image'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 2', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    await test.step('Validate the JPG image is present', async () => {
        //Currently identifying images on the new booking modal is hard, this should get easier with better accessibilty tags
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestJPG.*/)).toBeVisible();  
    });

    await test.step('Compare image against the golden file', async () => {
        await expect.soft(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestJPG.*/)).toHaveScreenshot('Neo-new-booking-modal-JPGimage.png');
    });

});

test('Check PNG image displays on booking modal', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Image'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Validate the PNG image is present', async () => {
        //Currently identifying images on the new booking modal is hard, this should get easier with better accessibilty tags
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestPNG.*/)).toBeVisible();  
    });

    await test.step('Compare image against the golden file', async () => {
        await expect.soft(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestPNG.*/)).toHaveScreenshot('Neo-new-booking-modal-PNGimage.png');
    });

});

test('Check SVG image displays on booking modal', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Image'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 3 - Cost code', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 3 - Cost code', exact: true }).click();
    });

    await test.step('Validate the SVG image is present', async () => {
        //Currently identifying images on the new booking modal is hard, this should get easier with better accessibilty tags
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestSVG.*/)).toBeVisible();  
    });

    await test.step('Compare image against the golden file', async () => {
        await expect.soft(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestSVG.*/)).toHaveScreenshot('Neo-new-booking-modal-SVGimage.png');
    });

});

test('Check image carousel', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Multiple images (carousel)'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
        
    });

    await test.step('Click Book on Basement Room 2', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 2', exact: true }).click();
    });

    //Please note: the locators for the image carousel controls are really really bad. Below is the best effort until it gets better.
    const carouselControlGroup = page.locator('.bg-athens-50 > div:nth-child(2)');
    const leftArrow = page.locator('.bg-athens-50 > div:nth-child(2) > button').nth(0);
    const pipGroup = page.locator('.bg-athens-50 > div:nth-child(2) > div');
    const firstPip = page.locator('.bg-athens-50 > div:nth-child(2) > div > button:nth-child(1)');
    const secondPip = page.locator('.bg-athens-50 > div:nth-child(2) > div > button:nth-child(2)');
    const thirdPip = page.locator('.bg-athens-50 > div:nth-child(2) > div > button:nth-child(3)');
    const rightArrow = page.locator('.bg-athens-50 > div:nth-child(2) > button').nth(1);

    await test.step('Validate the image carousel controls are present', async () => {
        await expect(carouselControlGroup).toBeVisible();
        await expect(leftArrow).toBeVisible();
        await expect(pipGroup).toBeVisible();
        await expect(firstPip).toBeVisible();
        await expect(secondPip).toBeVisible();
        await expect(thirdPip).toBeVisible();
        await expect(rightArrow).toBeVisible();
    });

    await test.step('Check the correct buttons in the carousel controls are enabled or disabled', async () => {
        await expect(leftArrow).toBeDisabled();
        await expect(firstPip).toBeEnabled();
        await expect(firstPip).toHaveCSS('background-color','rgb(15, 101, 196)');
        await expect(secondPip).toBeEnabled();
        await expect(secondPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(thirdPip).toBeEnabled();
        await expect(secondPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(rightArrow).toBeEnabled();
    });

    await test.step('Check the initial carousel controls against a golden file', async () => {
        await expect.soft(carouselControlGroup).toHaveScreenshot('Neo-new-booking-modal-ImageCarouselControls1.png');
    });
    
    await test.step('Validate the JPG image is shown first', async () => {
        //Currently identifying images on the new booking modal is hard, this should get easier with better accessibilty tags
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestJPG.*/)).toBeVisible();  
    });

    await test.step('Compare JPG image against the golden file', async () => {
        await expect.soft(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestJPG.*/)).toHaveScreenshot('Neo-new-booking-modal-JPGimage.png');
    });

    await test.step('Click right arrow, validate updates to arrows, pips & image', async () => {
        await rightArrow.click();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestPNG.*/)).toBeVisible();  
        await expect(leftArrow).toBeEnabled();
        await expect(firstPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(secondPip).toHaveCSS('background-color','rgb(15, 101, 196)');
        await expect(thirdPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(rightArrow).toBeEnabled();
    });

    await test.step('Check the updated carousel controls against a golden file', async () => {
        await expect.soft(carouselControlGroup).toHaveScreenshot('Neo-new-booking-modal-ImageCarouselControls2.png');
    });

    await test.step('Validate the PNG image is now shown', async () => {
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestJPG.*/)).toBeVisible();  
    });

    await test.step('Compare PNG image against the golden file', async () => {
        await expect.soft(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestPNG.*/)).toHaveScreenshot('Neo-new-booking-modal-PNGimage.png');
    });

    await test.step('Click right arrow again, validate updates to arrows, pips & image', async () => {
        await rightArrow.click();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestSVG.*/)).toBeVisible();  
        await expect(leftArrow).toBeEnabled();
        await expect(firstPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(secondPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(thirdPip).toHaveCSS('background-color','rgb(15, 101, 196)');
        await expect(rightArrow).toBeDisabled();
    });

    await test.step('Check the updated carousel controls against a golden file', async () => {
        await expect.soft(carouselControlGroup).toHaveScreenshot('Neo-new-booking-modal-ImageCarouselControls3.png');
    });

    await test.step('Validate the SVG image is now shown', async () => {
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestSVG.*/)).toBeVisible();  
    });

    await test.step('Compare SVG image against the golden file', async () => {
        await expect.soft(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestSVG.*/)).toHaveScreenshot('Neo-new-booking-modal-SVGimage.png');
    });

    await test.step('Click the first pip, validate updates to arrows, pips & image', async () => {
        await firstPip.click();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestJPG.*/)).toBeVisible();  
        await expect(leftArrow).toBeDisabled();
        await expect(firstPip).toHaveCSS('background-color','rgb(15, 101, 196)');
        await expect(secondPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(thirdPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(rightArrow).toBeEnabled();
    });

    await test.step('Click the third pip, validate updates to arrows, pips & image', async () => {
        await thirdPip.click();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestSVG.*/)).toBeVisible();  
        await expect(leftArrow).toBeEnabled();
        await expect(firstPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(secondPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(thirdPip).toHaveCSS('background-color','rgb(15, 101, 196)');
        await expect(rightArrow).toBeDisabled();
    });

    await test.step('Click the second pip, validate updates to arrows, pips & image', async () => {
        await secondPip.click();
        await expect(page.getByLabel('New booking', { exact: true }).getByLabel(/.*TestPNG.*/)).toBeVisible();  
        await expect(leftArrow).toBeEnabled();
        await expect(firstPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(secondPip).toHaveCSS('background-color','rgb(15, 101, 196)');
        await expect(thirdPip).toHaveCSS('background-color','rgb(255, 255, 255)');
        await expect(rightArrow).toBeEnabled();
    });

});