import { test as setup, expect } from '@playwright/test';


setup('Build a new org for a test run', async ({ browser, baseURL }) => {
  console.log("\nBuilding org... 🚀\n")
  const fs = require('fs');
  const context = await browser.newContext({ userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36' });
  const page = await context.newPage()
  //create uniq id
  const uuid = new Date().getTime();
  const orgName = `Regression Automation Neo ${uuid}`;
  const Password = '>s77(!TaPWV`5c4,';
  let seedPath = './test-data/neo/Seed.json';
  let needDelete = fs.existsSync(seedPath);
  let prevID = '';
  let prevOrgName = '';
  let prevOrgEnv = '';

  //log old org to file
  if (needDelete) {
    const seedFile = fs.readFileSync(seedPath, 'utf8');
    const org = JSON.parse(seedFile);
    prevID = org.orgID;//get previous org id
    prevOrgName = org.orgName;
    prevOrgEnv = org.orgENV;
  } else {
    console.log('No existing Seed.json file found, delete org process will be skipped\n');
  };

  process.stdout.write('Creating new org...       ');

  //create new org using new id
  const ServiceAdmin = require('../test-data/Users/<USER>');
  await page.goto('ui/#/login');

  await page.getByLabel('Email').fill(ServiceAdmin.Email);
  await page.getByLabel('Password').fill(ServiceAdmin.Password);
  await page.getByText('Log in', { exact: true }).click();
  await page.getByRole('link', { name: 'Switch' }).click();
  await expect(page.getByPlaceholder('Start typing an organisation')).toBeVisible();
  await expect(page.locator('[class="fa fa-spinner fa-spin"]')).toBeHidden();
  await page.getByRole('button', { name: 'New Organisation' }).click();
  await page.getByPlaceholder('New Organisation').fill(orgName);
  await page.getByRole('button', { name: 'Submit' }).click();
  await page.getByRole('link', { name: orgName }).click();
  await expect(page.getByRole('heading')).toContainText('You don\'t have any current or upcoming bookings.');
  await expect(page.getByRole('banner')).toContainText(orgName);

  //get orgID
  const response = await page.request.get(`${baseURL}api/v1/user/current?include=defaults&include=conferenceProviders`);
  await expect(response).toBeOK();

  let responseOrgID = await response.body().then(b => {
    let data = JSON.parse(b.toString());
    return data.organisationId;
  });

  //update new seed file
  const seed = ({
    "orgName": orgName,
    "seed": uuid,
    "orgID": responseOrgID,
    "orgENV": baseURL
  });

  fs.writeFileSync('./test-data/neo/Seed.json', JSON.stringify(seed));

  process.stdout.write('✅\n');

  process.stdout.write('Set feature switches...   ');

  //set feature switches 
  await page.getByRole('link', { name: 'Admin', exact: true }).click();
  await expect(page.getByText('Account Settings')).toBeVisible();
  await page.getByRole('link', { name: 'Account ' }).click();
  await expect(page.getByRole('table')).toContainText('LOCATION-V2');
  await page.getByRole('button', { name: 'Dev Only' }).click();
  await page.getByRole('row', { name: 'OVERRIDE_SEND_EMAIL_DISABLED' }).locator('button').click();
  await page.waitForLoadState('domcontentloaded')
  await page.waitForLoadState('networkidle');

  //check feature switches
  await page.reload();
  await expect(page.getByRole('table')).toContainText('LOCATION-V2');
  await expect(page.getByRole('row', { name: 'OVERRIDE_SEND_EMAIL_DISABLED' }).locator('button')).toBeVisible();

  process.stdout.write('✅\n');

  process.stdout.write('Set booking categories... ');

  //set booking categories
  await page.getByRole('link', { name: 'Booking Categories' }).click();
  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Meeting Rooms Room Meeting' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByText('Meeting Rooms')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Offices Office Office /' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByRole('cell', { name: 'Offices', exact: true }).locator('span')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Bedrooms Bedroom Bedroom /' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByRole('cell', { name: 'Bedrooms', exact: true }).locator('span')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Desks Desk Desk / Desks Use' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByRole('cell', { name: 'Desks', exact: true }).locator('span')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'People Person Person / People' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByRole('cell', { name: 'People', exact: true }).locator('span')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Equipment Equipment Equipment' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByRole('cell', { name: 'Equipment', exact: true }).nth(0).locator('span')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Access Passes Access Pass' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByRole('cell', { name: 'Access Passes', exact: true }).locator('span')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Privacy Booths Privacy Booth' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByText('Privacy Booths')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Parking Spaces Parking Space' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByText('Parking Spaces')).toBeVisible();

  await page.getByRole('heading', { name: 'Template Categories' }).locator('..').locator('..').getByRole('row', { name: 'Transport Passenger Seat' }).getByRole('button').click();
  await expect(page.getByRole('heading', { name: 'Booking Categories' }).locator('..').locator('..').getByText('Transport', { exact: true })).toBeVisible();

  page.reload();

  var dir = './test-data/neo/Users/';
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
  }

  process.stdout.write('✅\n');

  process.stdout.write('Add facilities...         ');

  //Add a couple of facilites to the org
  await page.getByRole('link', { name: 'Facilities' }).click();
  await expect(page.getByRole('heading', { name: 'Standard Facilities' })).toBeVisible();
  await expect(page.getByText('Adjustable Desk')).toBeVisible();
  await expect(page.getByText('Air Conditioning')).toBeVisible();
  await expect(page.getByText('Coffee Machine')).toBeVisible();
  await expect(page.getByText('Computer')).toBeVisible();
  await page.getByText('Adjustable Desk').click();
  await page.waitForTimeout(500); //manual waits to avoid strange race condition here
  await page.getByText('Air Conditioning').click();
  await page.waitForTimeout(500);
  await page.getByText('Coffee Machine').click();
  await page.waitForTimeout(500);
  await page.getByText('Computer').click();

  process.stdout.write('✅\n');

  process.stdout.write('Add a cost code...        ');

  await page.getByRole('link', { name: 'Cost Codes ', exact: true }).click();
  await page.getByPlaceholder('Type the name of a new Cost').fill('CostCode123');
  await page.getByPlaceholder('Type the Cost Code Description').fill('CostCode123 Description');
  await page.getByRole('button', { name: 'Add' }).click();

  process.stdout.write('✅\n');

  process.stdout.write('Add booking options...    ');
  await page.getByRole('link', { name: 'Booking Options' }).click();

  await page.getByRole('button', { name: 'New Catering Option' }).click();
  await page.getByPlaceholder('Title').fill('Catering option 1');
  await page.getByLabel('Start time only').check();
  await page.getByLabel('Item-based — defaults to').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Catering Option' }).click();
  await page.getByPlaceholder('Title').fill('Catering option 2');
  await page.getByLabel('Start and end times').check();
  await page.getByLabel('No quantity').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Catering Option' }).click();
  await page.getByPlaceholder('Title').fill('Catering option 3');
  await page.getByLabel('Same as duration of the').check();
  await page.getByLabel('Attendee-based — defaults to the number of attendees').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Equipment Option' }).click();
  await page.getByPlaceholder('Title').fill('Equipment option 1');
  await page.getByLabel('Start time only').check();
  await page.getByLabel('Item-based — defaults to').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Equipment Option' }).click();
  await page.getByPlaceholder('Title').fill('Equipment option 2');
  await page.getByLabel('Start and end times').check();
  await page.getByLabel('No quantity').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Equipment Option' }).click();
  await page.getByPlaceholder('Title').fill('Equipment option 3');
  await page.getByLabel('Same as duration of the').check();
  await page.getByLabel('Attendee-based — defaults to the number of attendees').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Services Option' }).click();
  await page.getByPlaceholder('Title').fill('Services option 1');
  await page.getByLabel('Start time only').check();
  await page.getByLabel('Item-based — defaults to').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Services Option' }).click();
  await page.getByPlaceholder('Title').fill('Services option 2');
  await page.getByLabel('Start and end times').check();
  await page.getByLabel('No quantity').check();
  await page.getByRole('button', { name: 'Save' }).click();

  await page.getByRole('button', { name: 'New Services Option' }).click();
  await page.getByPlaceholder('Title').fill('Services option 3');
  await page.getByLabel('Same as duration of the').check();
  await page.getByLabel('Attendee-based — defaults to the number of attendees').check();
  await page.getByRole('button', { name: 'Save' }).click();

  process.stdout.write('✅\n');

  process.stdout.write('Upload hierarchy...       ');

  //upload hierarchy 
  await page.getByRole('link', { name: 'Import Hierarchy ' }).click();
  await page.locator('#csvFile').setInputFiles('./test-data/neo/Automation Location Import.csv');
  await page.getByRole('button', { name: 'Submit' }).click();
  await expect(page.getByText('Successful import of')).toBeVisible({ timeout: 45000 });
  await page.getByRole('link', { name: 'Locations and Resources' }).click();

  await expect(page.getByRole('button', { name: 'Building Test Building 1' })).toBeVisible();
  await expect(page.getByRole('button', { name: 'Floor Ground Floor', exact: true })).toBeVisible();
  await expect(page.getByRole('button', { name: 'Desk Desk 1' })).toBeVisible();

  process.stdout.write('✅\n');

  process.stdout.write('Configure resources...    ');

  //make Desk 4 - Check in require check in
  await expect(async () => {
    await page.getByRole('button', { name: 'Desk 4 - Check in' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Show panel Settings' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Check-ins Override global' }).getByLabel('Override global settings').check();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //make Desk 5 - Check in with Auto Cancel require check in & have an auto cancel
  await expect(async () => {
    await page.getByRole('button', { name: 'Desk 5 - Check in with Auto Cancel' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Show panel Settings' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Check-ins Override global' }).getByLabel('Override global settings').check();
  await page.getByLabel('Cancel bookings that are not').check();
  await page.getByLabel('minutes', { exact: true }).click();
  await page.getByLabel('minutes', { exact: true }).press('ControlOrMeta+a');
  await page.getByLabel('minutes', { exact: true }).fill('1');
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //make Desk 6 - Onsite check in require check in & be onsite only
  await expect(async () => {
    await page.getByRole('button', { name: 'Desk 6 - Onsite check in' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Show panel Settings' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Check-ins Override global' }).getByLabel('Override global settings').check();
  await page.getByLabel('Only allow on-site check-in methods ').check();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //make Desk 7 - Onsite check in with AC require check in & be onsite only
  await expect(async () => {
    await page.getByRole('button', { name: 'Desk 7 - Onsite check in with Auto Cancel' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Show panel Settings' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Check-ins Override global' }).getByLabel('Override global settings').check();
  await page.getByLabel('Cancel bookings that are not').check();
  await page.getByLabel('minutes', { exact: true }).click();
  await page.getByLabel('minutes', { exact: true }).press('ControlOrMeta+a');
  await page.getByLabel('minutes', { exact: true }).fill('1');
  await page.getByLabel('Only allow on-site check-in methods ').check();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //add PNG image to Basement Room 1
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 1' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Media' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });

  const fileChooserPromise = page.waitForEvent('filechooser');
  await page.getByRole('link', { name: 'Add image Add image' }).click();
  const fileChooser = await fileChooserPromise;
  await fileChooser.setFiles(['./test-data/neo/Images/TestPNG.png']);

  await expect(page.getByText('%').last()).toBeVisible();
  const progressIndicators = await page.getByText('%');
  for (const indicator of await progressIndicators.all()) {
    await expect(indicator).toBeHidden({ timeout: 30000 });
  }

  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');

  //add description, alert, capacity (5), facility, layouts, options & all image types to Basement Room 2
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 2' }).getByRole('button').first().click();
    await page.getByPlaceholder('Description').fill('This is a description for basement room 2.');
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.getByPlaceholder('Alert').fill(`Here is an alert for basement room 2.`);
  await page.getByRole('combobox').first().selectOption('number:5');
  await page.getByRole('link', { name: 'Add facilities Add facilities' }).click();
  await page.getByText('Adjustable Desk').click();
  await page.getByRole('link', { name: 'Add layouts Add layouts' }).click();
  await page.getByRole('button', { name: 'Cabaret layout' }).click();
  await page.getByRole('button', { name: 'buffet layout' }).click();
  await page.getByRole('button', { name: 'Media' }).click();

  const fileChooserPromise1 = page.waitForEvent('filechooser');
  await page.getByRole('link', { name: 'Add image Add image' }).click();
  const fileChooser1 = await fileChooserPromise1;
  fileChooser.isMultiple(true);
  await fileChooser1.setFiles(['./test-data/neo/Images/TestJPG.jpg', './test-data/neo/Images/TestPNG.png', './test-data/neo/Images/TestSVG.svg']);

  await expect(page.getByText('%').last()).toBeVisible();
  const progressIndicators1 = await page.getByText('%');
  for (const indicator1 of await progressIndicators1.all()) {
    await expect(indicator1).toBeHidden({ timeout: 30000 });
  }

  await page.getByRole('button', { name: 'Show panel Options' }).click();
  await page.getByPlaceholder('Enter option name').fill('ca');
  await page.getByRole('option', { name: 'Catering option 1' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('ca');
  await page.getByRole('option', { name: 'Catering option 2' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('ca');
  await page.getByRole('option', { name: 'Catering option 3' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('eq');
  await page.getByRole('option', { name: 'Equipment option 1' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('eq');
  await page.getByRole('option', { name: 'Equipment option 2' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('eq');
  await page.getByRole('option', { name: 'Equipment option 3' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('ser');
  await page.getByRole('option', { name: 'Services option 1' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('ser');
  await page.getByRole('option', { name: 'Services option 2' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Enter option name').fill('ser');
  await page.getByRole('option', { name: 'Services option 3' }).locator('a').click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //make Basement Room 3 require cost code & add SVG image
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 3 - Cost code' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Media' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });

  const fileChooserPromise2 = page.waitForEvent('filechooser');
  await page.getByRole('link', { name: 'Add image Add image' }).click();
  const fileChooser2 = await fileChooserPromise2;
  await fileChooser2.setFiles(['./test-data/neo/Images/TestSVG.svg']);

  await expect(page.getByText('%').last()).toBeVisible();
  const progressIndicators2 = await page.getByText('%');
  for (const indicator2 of await progressIndicators2.all()) {
    await expect(indicator2).toBeHidden({ timeout: 30000 });
  }

  await page.getByRole('button', { name: 'Show panel Settings' }).click();
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Cost codes' }).getByLabel('Override global settings').check();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //make Basement Room 4 have an alert
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 4 - Alert' }).getByRole('button').first().click();
    await page.getByPlaceholder('Alert').click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.getByPlaceholder('Alert').fill(`Here is an alert on room Basement Room 4 - Alert for org ${uuid}`);
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //make Basement Room 5 need approval
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 5 - Need approval' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Show panel Settings' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Approvals' }).getByLabel('Override global settings').check();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //make Basement Room 6 need mandatory fields & add 2 facilities
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 6 - Mandatory fields' }).getByRole('button').first().click();
    await page.getByRole('link', { name: 'Add facilities Add facilities' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.getByRole('button', { name: 'All' }).click();
  await page.getByText('Coffee Machine').click();
  await page.getByText('Computer').click();
  await page.getByRole('button', { name: 'Show panel Settings' }).click();
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Attendee count Override' }).getByLabel('Override global settings').check();
  await page.getByLabel('Require attendee count to be').check();
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Cost codes Override global' }).getByLabel('Override global settings').check();
  await page.locator('mb-location-booking-settings').filter({ hasText: 'Texts Override global' }).getByLabel('Override global settings').check();
  await page.getByLabel('Require notes for each booking').check();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //Add facilities to Basement Room 7
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 7 - Facilities' }).getByRole('button').first().click();
    await page.getByRole('link', { name: 'Add facilities Add facilities' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.getByRole('button', { name: 'All' }).click();
  await page.getByText('Adjustable Desk').click();
  await page.getByText('Air Conditioning').click();
  await page.getByText('Coffee Machine').click();
  await page.getByText('Computer').click();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //add layouts to Basement Room 8
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 8 - Layout' }).getByRole('button').first().click();
    await page.getByRole('link', { name: 'Add layouts Add layouts' }).click();
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.getByRole('button', { name: 'All' }).click();
  await page.getByRole('button', { name: 'Banquet layout' }).click();
  await page.getByRole('button', { name: 'Buffet layout' }).click();
  await page.getByRole('button', { name: 'Cabaret layout' }).click();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //add capacity (10) to Basement Room 9
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 9 - Capacity 10' }).getByRole('button').first().click();
    await page.getByRole('combobox').first().selectOption('number:10');
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  //add a description to Basement Room 10
  await expect(async () => {
    await page.getByRole('button', { name: 'Basement Room 10 - Description' }).getByRole('button').first().click();
    await page.getByPlaceholder('Description').fill('This is a description for basement room 10. TestString@#$%^&*()👍😊こんにちは.');
  }).toPass({
    intervals: [1_000],
    timeout: 30_000
  });
  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForLoadState('domcontentloaded');
  await page.waitForLoadState('networkidle');
  await page.locator('[class="modal-backdrop fade  in"]').waitFor('detached');

  process.stdout.write('✅\n');

  process.stdout.write('Create users...           ');

  await page.getByRole('link', { name: 'Users', exact: true }).click();

  //create admin user
  const adminEmail = `automationadmin${uuid}@test.com`
  const AdminFirstName = 'admin'
  const AdminLastName = 'Test Automation User'
  const AdminName = 'Admin Test Automation User'

  fs.writeFileSync('./test-data/neo/Users/<USER>', JSON.stringify({ Email: adminEmail, Password: Password, Name: AdminName, FirstName: AdminFirstName, LastName: AdminLastName }));

  await page.getByRole('button', { name: 'New User' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill(adminEmail);
  await page.getByLabel('First Name').fill('')
  await page.getByLabel('First Name').fill(AdminFirstName);
  await page.getByLabel('Last Name').fill(AdminLastName);
  await page.getByLabel('Password', { exact: true }).fill(Password);
  await page.getByLabel('Re-type Password').fill(Password);
  await page.getByRole('button', { name: 'Administrator Can change' }).click();
  await page.getByRole('button', { name: 'Save' }).click();

  //create standard user
  const StandardEmail = `automationstandard${uuid}@test.com`
  const StandardFirstName = 'Standard'
  const StandardLastName = 'Test Automation User'
  const StandardName = 'Standard Test Automation User'

  fs.writeFileSync('./test-data/neo/Users/<USER>', JSON.stringify({ Email: StandardEmail, Password: Password, Name: StandardName, FirstName: StandardFirstName, LastName: StandardLastName }));

  await page.getByRole('button', { name: 'New User' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill(StandardEmail);
  await page.getByLabel('First Name').fill('');
  await page.getByLabel('First Name').fill(StandardFirstName);
  await page.getByLabel('Last Name').fill(StandardLastName);
  await page.getByLabel('Password', { exact: true }).fill(Password);
  await page.getByLabel('Re-type Password').fill(Password);
  await page.getByRole('button', { name: 'Save' }).click();

  //create email user (to only be used when checking emails)
  const EmailUserEmail = `matrixbookingautomation+${uuid}@outlook.com`;
  const OutlookPassword = 'Matrix@Booking@456';
  const EmailUserFirstName = 'Email';
  const EmailUserLastName = 'Test Automation User';
  const EmailUserName = `${EmailUserFirstName} ${EmailUserLastName}`;

  fs.writeFileSync('./test-data/neo/Users/<USER>', JSON.stringify({ Email: EmailUserEmail, EmailOutlook: '<EMAIL>', Password: Password, OutlookPassword: OutlookPassword, Name: EmailUserName, FirstName: EmailUserFirstName, LastName: EmailUserLastName }));

  await page.getByRole('button', { name: 'New User' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill(EmailUserEmail);
  await page.getByLabel('First Name').fill('');
  await page.getByLabel('First Name').fill(EmailUserFirstName);
  await page.getByLabel('Last Name').fill(EmailUserLastName);
  await page.getByLabel('Password', { exact: true }).fill(Password);
  await page.getByLabel('Re-type Password').fill(Password);
  await page.getByRole('button', { name: 'Save' }).click();

  //create location manager user
  const locationManagerEmail = `automationlocationmanager${uuid}@test.com`
  const locationManagerFirstName = 'LocationManager'
  const locationManagerLastName = 'TestAutomationUser'
  const locationManagerName = 'LocationManager TestAutomationUser'

  fs.writeFileSync('./test-data/neo/Users/<USER>', JSON.stringify({ Email: locationManagerEmail, Password: Password, Name: locationManagerName, FirstName: locationManagerFirstName, LastName: locationManagerLastName }));

  await page.getByRole('button', { name: 'New User' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill(locationManagerEmail);
  await page.getByLabel('First Name').fill('');
  await page.getByLabel('First Name').fill(locationManagerFirstName);
  await page.getByLabel('Last Name').fill(locationManagerLastName);
  await page.getByLabel('Password', { exact: true }).fill(Password);
  await page.getByLabel('Re-type Password').fill(Password);
  await page.getByRole('button', { name: 'Location manager Can manage' }).click();
  await page.getByPlaceholder('Type to add a location').nth(1).click();
  await page.getByPlaceholder('Type to add a location').nth(1).fill('wales');
  await page.getByRole('button', { name: 'Wales', exact: true }).locator('a').click();
  await page.getByRole('button', { name: 'Save' }).click();

  //create location admin user
  const locationAdminEmail = `automationlocationAdmin${uuid}@test.com`
  const locationAdminFirstName = 'locationAdmin'
  const locationAdminLastName = 'TestAutomationUser'
  const locationAdminName = 'locationAdmin TestAutomationUser'

  fs.writeFileSync('./test-data/neo/Users/<USER>', JSON.stringify({ Email: locationAdminEmail, Password: Password, Name: locationAdminName, FirstName: locationAdminFirstName, LastName: locationAdminLastName }));

  await page.getByRole('button', { name: 'New User' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill(locationAdminEmail);
  await page.getByLabel('First Name').fill('');
  await page.getByLabel('First Name').fill(locationAdminFirstName);
  await page.getByLabel('Last Name').fill(locationAdminLastName);
  await page.getByLabel('Password', { exact: true }).fill(Password);
  await page.getByLabel('Re-type Password').fill(Password);
  await page.getByRole('button', { name: 'Location administrator Can' }).click();
  await page.getByPlaceholder('Type to add a location').nth(3).click();
  await page.getByPlaceholder('Type to add a location').nth(3).fill('wales');
  await page.getByRole('button', { name: 'Wales', exact: true }).locator('a').click();
  await page.getByRole('button', { name: 'Save' }).click();

  //add an external contact
  await page.getByRole('link', { name: 'External Directory' }).click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByPlaceholder('Email').click();
  await page.getByPlaceholder('Email').fill(`visitor${uuid}@notanemail.com`);
  await page.getByPlaceholder('First Name').fill('Visitor');
  await page.getByPlaceholder('Last Name').fill('Contact');
  await page.getByPlaceholder('Company Name').fill('Visitor company');
  await page.getByPlaceholder('Contact Number').fill('*********');
  await page.getByRole('button', { name: 'Save' }).click();

  process.stdout.write('✅\n');

  process.stdout.write('Swap service admin back...');

  await page.getByRole('link', { name: 'Switch' }).click();
  await page.getByPlaceholder('Start typing an organisation').fill("Rich Test Org");
  await page.getByText("Rich Test Org").first().click()
  await expect(page.getByText('Welcome')).toBeVisible();

  await page.getByRole('link', { name: 'Switch' }).click();
  await page.getByRole('link', { name: 'Logout' }).click();
  const page2 = await context.newPage();
  page.close();

  process.stdout.write('✅\n\n');

  process.stdout.write('New org build completed! 🎉\n\n');

  //try to delete old org
  if (needDelete === true) {
    try {
      //mark old org for deletion

      console.log(`Attempting to delete previous test org...`)

      await page2.goto(prevOrgEnv)
      await page2.waitForURL(prevOrgEnv)

      await page2.getByLabel('Email').fill(ServiceAdmin.Email);
      await page2.getByLabel('Password').fill(ServiceAdmin.Password);
      await page2.getByRole('button', { name: 'Log in' }).click();
      await page2.getByRole('link', { name: 'Switch' }).click();

      await page2.getByPlaceholder('Start typing an organisation').fill(prevOrgName);
      await page2.getByRole('link', { name: prevOrgName, exact: true }).click();

      await expect(page2.getByText('Welcome')).toBeVisible();

      await page2.getByRole('link', { name: 'Admin', exact: true }).click();
      await page2.getByText('Account', { exact: true }).click();
      await page2.getByText('Mark for deletion', { exact: true }).click();
      await page2.getByText('Yes, mark for deletion', { exact: true }).click();
      console.log(`Org:${prevID} : ${prevOrgName} marked for deletion on ENV:${prevOrgEnv}\n`)

      //switch SA to new org and delete previous org
      await page2.getByRole('link', { name: 'Switch' }).click();
      await page2.getByPlaceholder('Start typing an organisation').fill("Rich Test Org");
      await page2.getByText("Rich Test Org").first().click();
      await expect(page2.getByText('Welcome')).toBeVisible();

      process.stdout.write(`Sending api request to delete org...`);

      const deleteResponse = await page2.request.delete(`${prevOrgEnv}api/v1/orgdelete?id=${prevID}`, {
        timeout: 30000 // Increase timeout to 30 seconds
      });
      
      process.stdout.write(` Response status: ${deleteResponse.status()}`);

      if (deleteResponse.status() === 200) {
        process.stdout.write(' ✅\n\n');
        console.log(`Delete request processed successfully! (200 code response)\n`);
      } else {
        process.stdout.write(' ❌\n\n');
        console.log(`Something went wrong! (No 200 code response)\n`);
      }

    } catch (error) {
      console.log(`\nAn unexpected error has occurred!\n` + error);
      console.log(`\nFailed to delete Orgid:${prevID} : ${prevOrgName} on ENV:${prevOrgEnv}\n`);
    }
  };
  await page2.close();
});