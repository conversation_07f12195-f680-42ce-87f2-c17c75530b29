import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to End - Edit repeat booking - Monthly', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Edit repeat booking - Every month' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(email_user_data);
    });

    // Create initial booking for testing edits
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2, 'hour');
    const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
    const bookingTitle = `Monthly Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
    // Set repeat until date to 6 months for good monthly pattern testing
    const repeatUntilDate = dayjs(bookingFromTime).add(6, 'month');

    // Create a monthly repeat booking for testing edits
    await test.step('Create initial monthly repeat booking', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await searchResultsPage.bookResource('Basement Room 2');
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
        // Add screenshot before repeat setup
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-before-repeat.png');
        await newBookingModalPage.setupRepeatBooking('Every month', repeatUntilDate, projectViewport.width);
        // Add screenshot after repeat setup
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-repeat-setup.png');
        await newBookingModalPage.setTitle(bookingTitle);
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-initial-booking.png');
        await newBookingModalPage.completeBooking();
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-booking-success.png');
        await newBookingModalPage.goToBookingsList(projectViewport.width);
        // Add screenshot of bookings list
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-bookings-list.png');
    });

    // Function to get dates that match the day-of-month of the original booking
    const getMonthlyDates = (startDate, endDate) => {
        const dates = [];
        let currentDate = startDate;
        const targetWeekday = startDate.day(); // Get day of week (0-6)
        const weekInMonth = Math.ceil(startDate.date() / 7); // Get which week of the month

        // Add initial date
        dates.push(currentDate);

        // Get dates for following months
        while (currentDate.isBefore(endDate)) {
            // Move to first day of next month
            currentDate = currentDate.add(1, 'month').startOf('month');
            
            // Find the first occurrence of our weekday
            let targetDate = currentDate.day(targetWeekday);
            if (targetDate.isBefore(currentDate)) {
                targetDate = targetDate.add(1, 'week');
            }

            // Move to correct week of month
            targetDate = targetDate.add(weekInMonth - 1, 'week');

            // Only add if within our date range
            if (targetDate.isBefore(endDate) || targetDate.isSame(endDate, 'day')) {
                dates.push(targetDate);            }
        }

        return dates;
    };

    // Edit details for single occurrence
    const editedSingleTitle = `${bookingTitle} - Single Edit`;
    const editedSingleFromTime = dayjs(bookingFromTime).add(1, 'hour');
    const editedSingleToTime = dayjs(editedSingleFromTime).add(30, 'minutes');

    await test.step('Edit single occurrence of monthly repeat booking', async () => {
        await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        // Add screenshot of edit dialog
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-dialog-open.png');
        await expect(newBookingModalPage.modalHeadingEdit).toContainText('Edit booking');
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-single-before.png');
        
        await newBookingModalPage.setStartTime(editedSingleFromTime);
        await newBookingModalPage.setEndTime(editedSingleToTime);
        await newBookingModalPage.setTitle(editedSingleTitle);
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-single-after.png');
        
        // Add screenshot of confirmation dialog
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-single-confirm.png');
        await newBookingModalPage.saveButton.click();
        await newBookingModalPage.bookingSuccessIcon.waitFor({ state: 'visible' });
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-single-success.png');
        // Add screenshot of updated bookings list
        await newBookingModalPage.goToBookingsList(projectViewport.width);
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-single-updated-list.png');
        
        // Verify single occurrence was updated
        await myBookingsPage.verifyBookingExists(editedSingleFromTime, editedSingleToTime, editedSingleTitle, projectViewport.width);

        // Verify next monthly occurrence remains unchanged - using correct date calculation
        const monthlyDates = getMonthlyDates(bookingFromTime, repeatUntilDate);
        const nextMonthBooking = monthlyDates[1]; // Get second date in series
        await myBookingsPage.verifyBookingExists(
            nextMonthBooking.hour(bookingFromTime.hour()).minute(bookingFromTime.minute()),
            nextMonthBooking.hour(bookingToTime.hour()).minute(bookingToTime.minute()),
            bookingTitle,
            projectViewport.width
        );
    });

    // Edit details for all remaining occurrences
    const editedSeriesTitle = `${bookingTitle} - Series Edit`;
    const editedSeriesFromTime = dayjs(bookingFromTime).add(2, 'hour');
    const editedSeriesToTime = dayjs(editedSeriesFromTime).add(30, 'minutes');

    await test.step('Edit all remaining occurrences of monthly repeat booking', async () => {
        const monthlyDates = getMonthlyDates(bookingFromTime, repeatUntilDate);
        const nextBookingDate = monthlyDates[2]; // Get third date in series for editing
        
        await myBookingsPage.editBooking(nextBookingDate, bookingToTime, bookingTitle, projectViewport.width);
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-series-before.png');

        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-series-dialog.png');
        await page.getByText('Edit all remaining bookings').click();
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-series-option-selected.png');
        
        // Verify start/end time fields are hidden
        await expect(newBookingModalPage.startTimeField).toBeHidden();
        await expect(newBookingModalPage.endTimeField).toBeHidden();
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-series-fields-hidden.png');
        
        await newBookingModalPage.setTitle(editedSeriesTitle);
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-series-after.png');
        
        await newBookingModalPage.saveButton.click();
        await newBookingModalPage.bookingSuccessIcon.waitFor({ state: 'visible' });
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-series-success.png');
        
        await newBookingModalPage.goToBookingsList(projectViewport.width);
        await expect.soft(page).toHaveScreenshot('Neo-edit-monthly-series-updated-list.png');
        
        // Verify remaining occurrences - use same date pattern
        const remainingDates = monthlyDates.slice(2); // Get all dates from third onwards
        for (const date of remainingDates) {
            await myBookingsPage.verifyBookingExists(
                date.hour(bookingFromTime.hour()).minute(bookingFromTime.minute()),
                date.hour(bookingToTime.hour()).minute(bookingToTime.minute()), // Fixed: bookingTo -> bookingToTime
                editedSeriesTitle,
                projectViewport.width
            );
        }
    });

    await test.step('Clean up - Cancel all repeat bookings', async () => {
        await myBookingsPage.goto();
        await page.waitForLoadState('networkidle');
        await myBookingsPage.cancelBooking(
            editedSeriesFromTime,
            editedSeriesToTime,
            editedSeriesTitle,
            true,
            projectViewport.width
        );
    });
});
