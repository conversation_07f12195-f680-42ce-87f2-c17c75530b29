//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import { createStandardUser } from './api_helpers/orgUsers';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('No change option present for standard user', { tag: '@parallel'}, async ({ request, baseURL, page, loginPage, newBookingModalPage }) => {

    const standard_user_data = await createStandardUser(request, baseURL); 

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check the change owner option is NOT presented for a normal user with no book on behalf', async () => {
       await expect(newBookingModalPage.changeOwnerButton).toBeHidden();
    });
   
});

test('Change option present for admin user', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Change booking owner - Elevated permission user'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const admin_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(admin_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check the change booking owner option is presented for my elevated permission user', async () => {
       await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
    });
   
});

test('Change option present for location admin user', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Change booking owner - Elevated permission user'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const locationadmin_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(locationadmin_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check the change booking owner option is presented for my elevated permission user', async () => {
       await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
    });
   
});

test('Change option present for location manager user', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Change booking owner - Elevated permission user'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const locationmanager_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(locationmanager_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Check the change booking owner option is presented for my elevated permission user', async () => {
       await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
    });
   
});

test('Click change owner as elevated user & cancel', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Change booking owner - Elevated permission user'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const admin_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(admin_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Click change & check owner search field', async () => {
       await newBookingModalPage.changeOwnerButton.click();
       await expect(newBookingModalPage.changeOwnerButton).toBeHidden();
       await expect(newBookingModalPage.changeOwnerSearchField).toBeVisible();
       await expect(newBookingModalPage.changeOwnerSearchField).toHaveAttribute('placeholder','Search by name or email address')
       await expect(newBookingModalPage.changeOwnerCancel).toBeVisible();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(newBookingModalPage.changeOwnerSearchField).toHaveScreenshot('Neo-new-booking-modal-bookingOwnerSearch.png');
    });

    await test.step('Click cancel & confirm search is no longer present and no change to owner', async () => {
        await newBookingModalPage.changeOwnerCancel.click();
        await expect(newBookingModalPage.changeOwnerSearchField).toBeHidden();
        await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('admin Test Automation User', { exact: true })).toBeVisible();
    });
   
});

test('Search for a new user & select', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Change booking owner - Elevated permission user'} }, async ({ page, loginPage, newBookingModalPage }) => {

    const admin_user_data = require('../test-data/neo/Users/<USER>'); 
    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(admin_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Click change & enter the name of a known user', async () => {
       await newBookingModalPage.changeOwnerButton.click();
       await newBookingModalPage.changeOwnerSearchField.fill(standard_user_data.Name);
       await expect(page.getByRole('option', { name: standard_user_data.Name })).toContainText(standard_user_data.Name);
       await expect(page.getByRole('option', { name: standard_user_data.Name })).toContainText(standard_user_data.Email);
    });

    await test.step('Compare listed option against a golden file', async () => {
        await expect.soft(page.getByRole('option', { name: standard_user_data.Name })).toHaveScreenshot('Neo-new-booking-modal-bookingOwnerSearchUser.png');
    });

    await test.step('Select the listed known user and confirm they are now set as the owner', async () => {
        await page.getByRole('option', { name: standard_user_data.Name }).click();
        await expect(page.getByText(`Booking owner${standard_user_data.Name}`)).toBeVisible();
        await expect(page.getByText(standard_user_data.Name, { exact: true })).toBeVisible();
        await expect(newBookingModalPage.changeOwnerButton).toBeVisible();
    });

});