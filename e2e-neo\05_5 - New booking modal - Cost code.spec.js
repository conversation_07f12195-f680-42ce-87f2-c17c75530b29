//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import { changeFeatureSwitch } from './api_helpers/orgFeatures';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Cost code field presented & accepts inputs', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Cost code'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
       await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Validate NO cost code field is displayed for this resource', async () => {
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Basement Room 1')).toBeVisible();
        await expect(page.getByText('Cost code *')).toBeHidden();
        await expect(page.getByLabel('Cost code *')).toBeHidden();
    });

     await test.step('Close the booking modal and click book on Basement Room 3 - Cost code', async () => {
        await page.getByRole('button', { name: 'Close panel' }).click();
        await page.getByRole('button', { name: 'Book Basement Room 3 - Cost' }).click();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Basement Room 3 - Cost code')).toBeVisible();
    });

    await test.step('Validate cost code field is displayed for this resource', async () => {
        await expect(page.getByText('Cost code *')).toBeVisible();
        await expect(page.getByLabel('Cost code *')).toBeVisible();
        await page.getByLabel('Cost code *').click();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-costcode.png');
    });

    await test.step('Validate the cost code field is free text and accepts any inputs', async () => {
        await page.getByLabel('Cost code *').fill('TestString@#$%^&*()👍😊こんにちは');
        await expect(page.getByLabel('Cost code *')).toHaveValue('TestString@#$%^&*()👍😊こんにちは');
    });

});

test('Cost code is validated', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Cost code'}  }, async ({ page, loginPage, baseURL, request }) => {

    await changeFeatureSwitch(request, baseURL, 'COST_CODE_VALIDATE', true);

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    
    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

     await test.step('Click book on Basement Room 3 - Cost code', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 3 - Cost' }).click();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Basement Room 3 - Cost code')).toBeVisible();
    });

    await test.step('Enter rubbish string for Code Code and attempt to submit the booking', async () => {
        await page.getByLabel('Cost code *').click();
        await page.getByLabel('Cost code *').fill('TestString@#$%^&*()👍😊こんにちは');
        await expect(page.getByLabel('Cost code *')).toHaveValue('TestString@#$%^&*()👍😊こんにちは');
    });

    await test.step('Validate cost code validation error shown on the modal', async () => {
        await page.getByRole('button', { name: 'Book', exact: true }).click();

        await expect(page.getByRole('alert')).toMatchAriaSnapshot(`
          - alert:
            - heading "Unable to book room" [level=3]
            - text: Please specify a valid cost code.
          `);
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-costcode-validateError.png');
    });

    await changeFeatureSwitch(request, baseURL, 'COST_CODE_VALIDATE', false);

});

test('Cost code list is enabled', { tag: '@serial', annotation:{type:'coa', description:'New booking modal - Cost code'}  }, async ({ page, loginPage, baseURL, request }) => {

    await changeFeatureSwitch(request, baseURL, 'COST_CODE_LIST', true);

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

     await test.step('Click book on Basement Room 3 - Cost code', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 3 - Cost' }).click();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Basement Room 3 - Cost code')).toBeVisible();
    });

    await test.step('Validate the cost code field is present and is now an dropdown type control', async () => {
        await expect(page.getByRole('button', { name: 'Cost code Select cost code' })).toBeVisible();  
    });

    await test.step('Click the dropdown and validate a known org cost code is listed', async () => {
        await page.getByRole('button', { name: 'Cost code Select cost code' }).click();
        
        if(projectViewport.width<767){
            await expect(page.getByRole('button', { name: 'CostCode123 Description' })).toBeVisible();
        }
        else{
            await expect(page.getByRole('option', { name: 'CostCode123 Description' })).toBeVisible();
        }
        
    });

    await test.step('Select the listed cost code and validate it is selected in the field', async () => {
        
        if(projectViewport.width<767){
            await page.getByRole('button', { name: 'CostCode123 Description' }).click();
        }
        else{
            await page.getByRole('option', { name: 'CostCode123 Description' }).click();
        }
        
        await expect(page.getByRole('button', { name: 'Cost code CostCode123' })).toBeVisible();
    });
    
    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-costcode-dropdown.png');
    });

    await changeFeatureSwitch(request, baseURL, 'COST_CODE_LIST', false);

});

test('User has a default cost code', { tag: '@serial', annotation:{type:'coa', description:'New booking modal - Cost code'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');
    
    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Swap to old ui and set a default cost code', async () => {
        await expect(page).toHaveURL('/neo/search');
        await expect(page.getByRole('img', { name: 'Matrix Booking logo' })).toBeVisible();
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
        await page.goto('/ui/#/profile');
        await page.getByRole('link', { name: 'Preferences' }).click();
        await page.getByPlaceholder('Cost Code').click();
        await page.getByPlaceholder('Cost Code').fill('DEFAULTCOSTCODE123');
        await page.getByRole('button', { name: 'Save' }).click();
        await expect(page.getByText('Your preferences have been')).toBeVisible();
    });
    
    await test.step('Swap back to neo UI & perform a search', async () => {
        await page.goto('/neo/');
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

     await test.step('Click book on Basement Room 3 - Cost code', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 3 - Cost' }).click();
        await expect(page.getByLabel('New booking', { exact: true }).getByText('Basement Room 3 - Cost code')).toBeVisible();
    });

    await test.step('Validate cost code field presented & it contains the users default cost code', async () => {
        await expect(page.getByText('Cost code *')).toBeVisible();
        await expect(page.getByLabel('Cost code *')).toBeVisible();
        await expect(page.getByLabel('Cost code *')).toHaveValue('DEFAULTCOSTCODE123');
    });

    await test.step('Swap back old ui and clear default cost code', async () => {
        await page.goto('/ui/#/profile');
        await page.getByRole('link', { name: 'Preferences' }).click();
        await page.getByPlaceholder('Cost Code').click();
        await page.getByPlaceholder('Cost Code').fill('');
        await page.getByRole('button', { name: 'Save' }).click();
        await expect(page.getByText('Your preferences have been')).toBeVisible();
    });

});