import dayjs from 'dayjs';

const timeNow = dayjs();

  /**
   * @class
   * @param {import ('@playwright/test').Page} page 
   */

export class OutlookEmailPage {

  constructor(page) {
    this.page = page;
    this.emailField = page.locator('[type="email"]');
    this.nextButton = page.getByRole('button').filter({ hasText: /(^Sign in$|^Next$)/ });
    this.passwordField = page.locator('[type="password"]:not([aria-hidden="true"])');
    this.signInButton = page.getByRole('button').filter({ hasText: /(^Sign in$|^Next$)/ });
    this.yesButton = page.getByText("Yes");
  }

  /**
   * @param {import ('@playwright/test').Page} page 
   */

  async outlookLogin(user) {
    await this.goto();
    await this.emailField.fill(user.EmailOutlook);
    await this.nextButton.click();
    await this.passwordField.fill(user.OutlookPassword);
    await this.signInButton.click();
    await this.yesButton.click();
  }

  async goto() {
    await this.page.goto('https://go.microsoft.com/fwlink/p/?LinkID=2125442&deeplink=owa%2F%3Fstate%3D1%26redirectTo%3DaHR0cHM6Ly9vdXRsb29rLmxpdmUuY29tL21haWwv');
  }

}