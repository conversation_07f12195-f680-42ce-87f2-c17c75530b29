// Video handling is now managed globally in the fixture (pages.fixture.js)
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to End - Cancel standard booking', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Cancel standard booking' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    const email_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Login and create a test booking to cancel', async () => {
        await loginPage.simpleLogin(email_user_data);

        // Create initial booking for testing cancellation
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Cancel Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;

        // Create booking
        await page.getByRole('button', { name: 'Search' }).click();
        await searchResultsPage.bookResource('Basement Room 2');
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.completeBooking();
        await newBookingModalPage.goToBookingsList(projectViewport.width);

        // Verify booking exists before testing cancellation
        await myBookingsPage.verifyBookingExists(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);

        // Test cancellation dialog - Cancel clicked but then closed
        await test.step('Verify cancel dialog and close button', async () => {
            // Open cancel dialog
            await myBookingsPage.openCancelDialog(bookingFromTime, bookingTitle, projectViewport.width);

            // GIVEN dialog is displayed - verify all required elements
            await expect(page.getByText('Cancel bookingThis action').locator('..')).toBeVisible();
            await expect(page.getByRole('heading', { name: 'Cancel booking' })).toBeVisible();
            await expect(page.getByText('This action cannot be undone and will make the room available for others to book.')).toBeVisible();
            await expect(page.getByRole('button', { name: 'Close' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Cancel booking' })).toBeVisible();
            await expect.soft(page).toHaveScreenshot('Neo-cancel-standard-dialog.png');

            // WHEN click close
            await page.getByRole('button', { name: 'Close' }).click();
            // THEN dialog closes
            await expect(page.getByText('Cancel bookingThis action')).toBeHidden();

            // Handle mobile extra close if needed
            if (projectViewport.width < 768) {
                await page.getByRole('button', { name: 'Close' }).click();
            }
            // AND booking still exists
            await myBookingsPage.verifyBookingExists(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        });

        // Test actual cancellation
        await test.step('Cancel booking and verify removal', async () => {


            // WHEN click cancel booking
            await myBookingsPage.cancelBooking(
                bookingFromTime,
                bookingToTime,
                bookingTitle,
                false,
                projectViewport.width
            );

            // THEN dialog closes and booking is removed
            await expect(page.getByText('Cancel bookingThis action')).toBeHidden();
            await myBookingsPage.verifyBookingDoesNotExist(bookingFromTime, bookingTitle, projectViewport.width);
            await expect.soft(page).toHaveScreenshot('Neo-cancel-standard-completed.png');
        });
    });
});
