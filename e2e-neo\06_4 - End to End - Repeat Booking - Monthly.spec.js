// Video handling is now managed globally in the fixture (pages.fixture.js)
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Create a repeat booking - Monthly', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Create a repeat booking - Monthly' } },
    async ({ page, loginPage, searchResultsPage, newBookingModalPage, outlookEmailPage, myBookingsPage }) => {
        test.slow(); //Sets the timeout to triple the project timeout. This is due to accessing the outlook webmail UI.

        const email_user_data = require('../test-data/neo/Users/<USER>');
        const standard_user_data = require('../test-data/neo/Users/<USER>');
        const org = require('../test-data/neo/Seed.json');

        await test.step('Login to the system', async () => {
            await loginPage.simpleLogin(email_user_data);
        });

        //Define details for a new booking
        const timeNow = dayjs();
        const bookingFromTime = timeNow.add(2, 'hour');
        // Create a new dayjs object for the end time to avoid mutation issues
        const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
        const bookingTitle = `Monthly Repeat Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;
        const bookingNotes = `Monthly repeat booking test notes - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm꞉ss')}`;

        // Create a new dayjs object for the repeat until date to avoid mutation issues
        let repeatUntilDate = dayjs(bookingFromTime).add(6, 'month');

        // Generate a random number for external attendee email
        const randomNumber = Math.floor(Math.random() * 9999);
        const externalAttendeeEmail = `visitor${randomNumber}@test.com`;
        const externalAttendeeName = `Visitor ${randomNumber}`;

        await test.step('Perform a search', async () => {
            await page.getByRole('button', { name: 'Search' }).click();
            await expect(page).toHaveTitle('Search results - Matrix Booking');
            await page.locator('[data-testid="loader"]').waitFor({ state: 'hidden' });
            await expect.soft(page).toHaveScreenshot('Neo-monthly-booking-searchresults.png');
        });

        await test.step('Click Book on Basement Room 2', async () => {
            await searchResultsPage.bookResource('Basement Room 2');
        });

        await test.step('Set the Start time', async () => {
            await newBookingModalPage.setStartTime(bookingFromTime);
        });

        await test.step('Set the End time', async () => {
            await newBookingModalPage.setEndTime(bookingToTime);
        });

        await test.step('Enable repeat booking and set frequency to Every month', async () => {
            await newBookingModalPage.setupRepeatBooking('Every month', repeatUntilDate, projectViewport.width);
            await expect.soft(page).toHaveScreenshot('Neo-monthly-booking-setup.png');
        });

        await test.step('Add a title', async () => {
            await newBookingModalPage.setTitle(bookingTitle);
        });

        await test.step('Add some notes', async () => {
            await newBookingModalPage.setNotes(bookingNotes);
        });

        await test.step('Add the internal attendee Standard User', async () => {
            await newBookingModalPage.addInternalAttendee(standard_user_data.FirstName, projectViewport.width);
        });

        await test.step('Add an external attendee', async () => {
            await newBookingModalPage.addExternalAttendee(
                externalAttendeeEmail,
                externalAttendeeName,
                'Contact',
                projectViewport.width
            );
            await expect.soft(page).toHaveScreenshot('Neo-monthly-booking-form.png');
        });

        await test.step('Click book & validate success message', async () => {
            await newBookingModalPage.completeBooking();
            await newBookingModalPage.verifyBookingSuccess(bookingFromTime, bookingToTime, email_user_data.Name, true);
            await expect.soft(page).toHaveScreenshot('Neo-monthly-booking-success.png');
        });

        await test.step('Click the booking button and validate the bookings are listed', async () => {
            // Pass the viewport width to handle mobile/desktop navigation correctly
            await newBookingModalPage.goToBookingsList(projectViewport.width);

            // Add a wait to ensure the page has fully loaded and stabilized
            await page.waitForTimeout(2000);
            await page.waitForLoadState('networkidle');
            await page.waitForLoadState('domcontentloaded');

            // Verify the booking exists
            await myBookingsPage.verifyBookingExists(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
        });

        await test.step('verify booking exists on monthly schedule', async () => {
            // Function to get the same weekday each month
            const getMonthlyDates = (startDate, endDate) => {
                const dates = [];
                let currentDate = startDate;
                const targetWeekday = startDate.day(); // Get day of week (0-6)
                const weekInMonth = Math.ceil(startDate.date() / 7); // Get which week of the month

                // Add initial date
                dates.push(currentDate);

                // Get dates for following months
                while (currentDate.isBefore(endDate)) {
                    // Move to first day of next month
                    currentDate = currentDate.add(1, 'month').startOf('month');
                    
                    // Find the first occurrence of our weekday
                    let targetDate = currentDate.day(targetWeekday);
                    if (targetDate.isBefore(currentDate)) {
                        targetDate = targetDate.add(1, 'week');
                    }

                    // Move to correct week of month
                    targetDate = targetDate.add(weekInMonth - 1, 'week');

                    // Only add if within our date range
                    if (targetDate.isBefore(endDate) || targetDate.isSame(endDate, 'day')) {
                        dates.push(targetDate);                    }
                }

                return dates;
            };

            const bookingDates = getMonthlyDates(bookingFromTime, repeatUntilDate);
            // Update repeatUntilDate to match the actual last occurrence
            repeatUntilDate = bookingDates[bookingDates.length - 1];

            // Verify each monthly booking exists
            for (const date of bookingDates) {
                const bookingTime = date
                    .hour(bookingFromTime.hour())
                    .minute(bookingFromTime.minute());

                await myBookingsPage.verifyBookingExists(
                    bookingTime,
                    bookingToTime,
                    bookingTitle,
                    projectViewport.width
                );
            }
        });

        await test.step('Edit a booking and verify attributes', async () => {
            await myBookingsPage.editBooking(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
            await expect.soft(page).toHaveScreenshot('Neo-monthly-booking-edit.png');

            await expect(newBookingModalPage.titleField).toHaveValue(bookingTitle);
            await expect(newBookingModalPage.notesField).toHaveValue(bookingNotes);
            await expect(page.getByRole('button', { name: 'Email Test Automation User (you) (owner)' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Standard Test Automation User' })).toBeVisible();
            await expect(page.getByRole('button', { name: `${externalAttendeeName} Contact` })).toBeVisible();
            await expect(page.getByText(`Repeats every month until ${repeatUntilDate.format('ddd, D MMM YYYY')}`)).toBeVisible();

            await newBookingModalPage.closeButton.click();
        });

        await test.step('Login to outlook webmail with the email user', async () => {
            await outlookEmailPage.outlookLogin(email_user_data);
        });

        await test.step('Find expected booking email & validate details are correct', async () => {
            await page.getByLabel(`Has attachments Matrix Booking Basement Room 2 booked from ${bookingFromTime.format('h:mm A, dddd D')}`).getByText('repeats every month until').click();
            await expect(page.getByLabel('Reading Pane').getByRole('heading')).toContainText(`Basement Room 2 booked from ${bookingFromTime.format('h:mm A, dddd D MMMM YYYY')}`);
            await expect(page.locator('#x_desktopContent')).toContainText('Booking Confirmed');
            await expect.soft(page.locator('#x_desktopContent')).toContainText(`Basement Room 2, ${bookingFromTime.format('h:mma')} - ${bookingToTime.format('h:mma')}, ${bookingFromTime.format('dddd D MMMM YYYY')}`);
            await expect(page.locator('#x_mainMiddleTable')).toContainText(standard_user_data.Name);
            await expect(page.locator('#x_mainMiddleTable')).toContainText(bookingTitle);
            await expect(page.locator('#x_mainMiddleTable')).toContainText('Basement Room 2');
            await expect(page.locator('#x_mainMiddleTable')).toContainText('Basement, Test Building 1');
            await expect(page.locator('#x_mainMiddleTable')).toContainText(bookingFromTime.format('h:mma') + ' - ' + bookingToTime.format('h:mma') + ', ' + bookingFromTime.format('dddd D MMMM YYYY'));
            await expect(page.locator('#x_mainMiddleTable')).toContainText(org.orgName);
            await expect(page.locator('#x_mainMiddleTable')).toContainText(`Repeats every month until ${repeatUntilDate.format('ddd, D MMM YYYY')}`);
            await expect.soft(page).toHaveScreenshot('Neo-monthly-booking-confirmation-email.png');
        });

        await test.step('Clean up - Clear email inbox', async () => {
            if (projectViewport.width < 768) {
                await page.getByLabel('Close').click();
                await page.waitForTimeout(1000);
            }

            await page.keyboard.press('Control+KeyA');
            await page.waitForTimeout(1000);
            await page.keyboard.press('Delete');
            await page.waitForTimeout(1000);
            await page.keyboard.press('Enter');
        });

        await test.step('Clean up - Cancel all repeat bookings', async () => {
            await myBookingsPage.goto();
            await page.waitForLoadState('networkidle');
            await myBookingsPage.cancelBooking(
                bookingFromTime,
                bookingToTime,
                bookingTitle,
                true,
                projectViewport.width
            );
        });
    }); // End of test
