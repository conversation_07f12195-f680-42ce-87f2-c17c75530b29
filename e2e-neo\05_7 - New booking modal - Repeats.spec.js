//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';

import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Validate repeat booking toggle is present', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Repeat booking'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Click the repeat booking toggle and validate listed fields', async () => {
        await expect(page.getByText('Repeat booking')).toBeVisible();
        await expect(page.getByLabel('Repeat booking')).toBeVisible();
        await expect(page.getByLabel('Repeat booking')).toHaveValue('1');
    });
    
    await test.step('Compare the disabled repeat section against a golden file', async () => {
    await expect.soft(page.locator('div').filter({ hasText: /^Repeat booking$/ }).first()).toHaveScreenshot('Neo-new-booking-modal-repeatSectionOff.png');
    await expect(page.locator('div').filter({ hasText: /^Repeat booking$/ }).first()).toMatchAriaSnapshot(`
      - button "Repeat booking"
      - text: Repeat booking
      `);
    });
});

test('Toggle repeat booking & check section', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Repeat booking'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Click the repeat booking toggle and validate listed fields for default values', async () => {
        await page.getByLabel('Repeat booking').click()
        await expect(page.getByLabel('Repeat booking')).toHaveValue('2');

        if (projectViewport.width > 767){
             await expect(page.locator('[id="headlessui-label-\\:r3o\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r3o\\:"]')).toContainText('Repeat frequency');
            await expect(page.locator('[id="repeatEndDate-select\\:md"]')).toContainText('Every week day');
            await expect(page.locator('[id="headlessui-label-\\:r42\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r42\\:"]')).toContainText('Repeat until *');
        }
        else {
            await expect(page.locator('[id="headlessui-label-\\:r3r\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r3r\\:"]')).toContainText('Repeat frequency');
            await expect(page.locator('[id="repeatEndDate-select\\:sm"]')).toContainText('Every week day');
            await expect(page.locator('[id="headlessui-label-\\:r45\\:"]')).toBeVisible();
            await expect(page.locator('[id="headlessui-label-\\:r45\\:"]')).toContainText('Repeat until *');
        }
        
        await expect(page.getByRole('button', { name: 'Repeat frequency Every week day' })).toBeVisible();
        await expect(page.locator('#repeatEndDate-field')).toBeVisible();
        await expect(page.locator('#repeatEndDate-field')).toContainText('Select end date');
    });
    
    await test.step('Compare the repeat section against a golden file', async () => {
        await expect.soft(page.getByText('Repeat bookingChoose Repeat')).toHaveScreenshot('Neo-new-booking-modal-repeatSectionOn.png');
        
        if (projectViewport.width > 767){   
            await expect(page.getByText('Repeat bookingChoose Repeat')).toMatchAriaSnapshot(`
            - button "Repeat booking"
            - text: Repeat booking Choose Repeat frequency Repeat frequency
            - button "Repeat frequency Every week day expandable"
            - text: Choose Repeat until Repeat until *
            - button "Repeat until Select end date expandable"
            `);
        }

    });

});

test('Open & set repeat frequency', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Repeat booking'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Click the repeat booking toggle', async () => {
        await page.getByLabel('Repeat booking').click()
        await expect(page.getByLabel('Repeat booking')).toHaveValue('2');
    });

    let optionOrButton;

    if (projectViewport.width > 767){   
                optionOrButton = 'option'
    } else {
                optionOrButton = 'button'
    }

    await test.step('Click Repeat frequency and check listed options', async () => {
        await page.getByRole('button', { name: 'Repeat frequency Every week' }).click();
        await expect(page.getByRole(optionOrButton, { name: 'No repeat' })).toBeVisible();
        await expect(page.getByRole(optionOrButton, { name: 'Every day' })).toBeVisible();
        await expect(page.getByRole(optionOrButton, { name: 'Every week day' })).toBeVisible();
        await expect(page.getByRole(optionOrButton, { name: 'Weekly' })).toBeVisible();
        await expect(page.getByRole(optionOrButton, { name: 'Every two weeks' })).toBeVisible();
        await expect(page.getByRole(optionOrButton, { name: 'Every four weeks' })).toBeVisible();
        await expect(page.getByRole(optionOrButton, { name: 'Every month' })).toBeVisible();
    });

    await test.step('Validate Every week day is selected', async () => {
        
        if (projectViewport.width > 767){   
            await expect(page.getByRole(optionOrButton, { name: 'Every week day' })).toHaveAttribute('aria-selected', 'true');
        } else {
            await expect(page.getByRole(optionOrButton, { name: 'Every week day' })).toHaveAttribute('aria-pressed', 'true');
        }
    
       await expect(page.getByRole(optionOrButton, { name: 'Every week day' })).toHaveCSS('color', 'rgb(15, 101, 196)');
       await expect(page.getByRole(optionOrButton, { name: 'Every week day' })).toHaveCSS('background-color', 'rgb(214, 234, 255)');
    });

    await test.step('Compare selected item a golden file', async () => {
        await expect.soft(page.getByRole(optionOrButton, { name: 'Every week day' })).toHaveScreenshot('Neo-new-booking-modal-repeatFrequencySelectedOption1.png');
    });
    
    await test.step('Compare repeat frequency list to a golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-new-booking-modal-repeatFrequencyList.png');
    });

    await test.step('Select weekly and validate it is selected in the field', async () => {
        await page.getByRole(optionOrButton, { name: 'Weekly' }).click();
        await expect(page.getByRole('button', { name: 'Repeat frequency Weekly' })).toBeVisible();
        await page.getByRole('button', { name: 'Repeat frequency Weekly' }).click();
       
       if (projectViewport.width > 767){   
            await expect(page.getByRole(optionOrButton, { name: 'Weekly' })).toHaveAttribute('aria-selected', 'true');
        } else {
            await expect(page.getByRole(optionOrButton, { name: 'Weekly' })).toHaveAttribute('aria-pressed', 'true');
        }
    });

});

test('Open & set repeat until', { tag: '@parallel', annotation:{type:'coa', description:'New booking modal - Repeat booking'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');  

    await test.step('Login to the system', async () => {
        await loginPage.simpleLogin(standard_user_data);
    });

    await test.step('Perform a search', async () => {
        await page.getByRole('button', { name: 'Search' }).click();
        await expect(page).toHaveTitle('Search results - Matrix Booking');
    });

    await test.step('Click Book on Basement Room 1', async () => {
        await page.getByRole('button', { name: 'Book Basement Room 1', exact: true }).click();
    });

    await test.step('Click the repeat booking toggle and validate listed fields for default values', async () => {
        await page.getByLabel('Repeat booking').click()
    });

    let dateNow = dayjs();
    
    await test.step('Click repeat until & check basic calendar elements are presented', async () => {
        await page.locator('#repeatEndDate-field').click();   
        await expect(page.getByText(`${dateNow.format('MMMM YYYY')}`,{ exact:true })).toBeVisible();
        await expect(page.getByLabel('Go to next month')).toBeVisible();
        await expect(page.getByLabel('Go to next month')).toBeEnabled();
        await expect(page.getByLabel('Go to previous month')).toBeVisible();
        await expect(page.getByLabel('Go to previous month')).toBeDisabled(); 
        await expect(page.getByRole('button', { name: 'Today' })).toBeVisible();
        await expect(page.getByRole('button', { name: 'Tomorrow' })).toBeVisible();
        await expect(page.getByLabel('Monday')).toBeVisible();
        await expect(page.getByLabel('Tuesday')).toBeVisible();
        await expect(page.getByLabel('Wednesday')).toBeVisible();
        await expect(page.getByLabel('Thursday')).toBeVisible();
        await expect(page.getByLabel('Friday')).toBeVisible();
        await expect(page.getByLabel('Saturday')).toBeVisible();
        await expect(page.getByLabel('Sunday')).toBeVisible();

        //Elements checked below only apply to small view
        if (projectViewport.width < 768){
            await expect(page.getByRole('heading', { name: 'Select repeat until' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Close panel' })).toBeVisible();
            await expect(page.getByRole('button', { name: 'Done' })).toBeVisible();
        }

    });

    await test.step('Attempt to check all expected days are listed', async () => {
        let currentDate = dateNow.startOf('month');
        const endDate = dateNow.endOf('month');
        let listOfDates = '';
        const calendarDatePanel = page.locator('[class="w-full border-collapse space-y-1"]')
        
        //Loop through all expected days in the months and add to a string
        while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
            listOfDates = listOfDates + " " + currentDate.format('D');
            currentDate = currentDate.add(1, 'day');
        }

        await expect(calendarDatePanel).toContainText(listOfDates);
    });

    await test.step('Compare repeat until control against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-booking-modal-repeatUntilCalendar.png');
    });

    await test.step('Check the day before is disabled & I cannot select it', async () => {
        //Please note: 
        //The messy logic below is an attempt to make sure yesterday's calendar button is disabled and when clicked nothing happens
        //This sounds easy but you can't rely on the button numbers as the calendar AND the dropdown UI can have the same attributes sometimes
        
        //Load all the buttons on the page in to a object
        const allButtons = page.locator('button');
        //Find the index of the selected today button (using the silly class name), with the object
        const selectedTodayCalendarButton = page.locator('[class="rdp-button_reset rdp-button focus-visible:border-white focus-visible:border ring-offset-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-denim inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50 hover:bg-bg-hover active:bg-bg-active h-9 w-9 p-0 aria-selected:opacity-100 rounded m-0.5 bg-denim hover:bg-denim active:bg-denim text-white underline decoration-2 underline-offset-[6px] text-denim"]');
        const targetButtonIndex = await allButtons.evaluateAll((elements, selectedTodayCalendarButton) => {
            return elements.findIndex(el => el === selectedTodayCalendarButton);
          }, await selectedTodayCalendarButton.elementHandle());
        //Create a locator for the previous date button by minusing 1 from the index
        const previousCalendarButton = allButtons.nth(targetButtonIndex - 1);
      
        await expect(previousCalendarButton).toBeVisible();
        await expect(previousCalendarButton).toHaveText(dateNow.subtract(1, 'day').format('D'));
        await expect(previousCalendarButton).toBeDisabled();
        await previousCalendarButton.click({force:true});
        await expect(selectedTodayCalendarButton).toHaveAttribute('aria-selected', 'true');
    });

    let selectedDate = dayjs();

    await test.step('Select another day (the 15th of next month to avoid issues) and validate it is selected', async () => {
        await page.getByLabel('Go to next month').click();
        await page.getByRole('button', { name: '15', exact: true }).click();
        
        if (projectViewport.width < 768){
            await page.getByRole('button', { name: 'Done' }).click();
        }

        selectedDate = dateNow.add(1,'month').date(15);
        await expect(page.locator(`[aria-label="Repeat until ${selectedDate.format('ddd, D MMM')} expandable"]:visible`)).toBeVisible();

        if (projectViewport.width < 768){
            await expect(page.getByLabel(`Repeat until ${selectedDate.format('ddd, D MMM')} expandable`).nth(1)).toBeVisible();
        }
        else
        {
            await expect(page.getByRole('button', { name: `Repeat until ${selectedDate.format('ddd, D MMM')} expandable` })).toBeVisible();
        }
    
    });

});