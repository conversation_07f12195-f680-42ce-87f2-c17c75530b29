//import test and expect functionality 
import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('Check login page', { tag: '@parallel' }, async ({ page, loginPage }) => {

    await test.step('Navigate to the Neo login page', async () => {
        await loginPage.goto();
    });

    await test.step('Validate expected UI elements are presented', async () => {
        await expect(loginPage.emailField).toBeVisible();
        await expect(loginPage.passwordField).toBeVisible();
        await expect(loginPage.forgottenPasswordLink).toBeVisible();
        await expect(loginPage.loginButton).toBeVisible();
        await expect(loginPage.logo).toBeVisible();
    });

    await test.step('Compare screen against golden file', async () => {
        await expect.soft(page).toHaveScreenshot('Neo-login-Page.png');
    });

});

test('Unable to login with invalid credentials', { tag: '@parallel', annotation:{type:'coa', description:'Login via username & password'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Navigate to the Neo login page', async () => { 
        await loginPage.goto();
    });

    await test.step('Attempt to login with no email & password', async () => {
        await loginPage.loginButton.click();
        await expect(page.getByRole('heading', { name: 'Unable to log in' })).toBeVisible();
        await expect(page.getByText('Invalid email address')).toBeVisible();
    });

    await test.step('Attempt to login with invalid email & no password', async () => {
        await loginPage.emailField.fill('notarealemail');
        await loginPage.loginButton.click();
        await expect(page.getByRole('heading', { name: 'Unable to log in' })).toBeVisible();
        await expect(page.getByText('Invalid email address')).toBeVisible();
    });

    await test.step('Attempt to login with valid email & no password', async () => {
        await loginPage.emailField.fill(standard_user_data.Email);
        await loginPage.passwordField.fill('');
        await loginPage.loginButton.click();
        await expect(page.getByRole('heading', { name: 'Unable to log in' })).toBeVisible();
        await expect(page.getByText('You could not be logged in with those credentials')).toBeVisible();
    });

    await test.step('Attempt to login with no email & valid password', async () => {
        await loginPage.emailField.fill('');
        await loginPage.passwordField.fill(standard_user_data.Password);
        await loginPage.loginButton.click();
        await expect(page.getByRole('heading', { name: 'Unable to log in' })).toBeVisible();
        await expect(page.getByText('Invalid email address')).toBeVisible();
    });

    await test.step('Attempt to login with fake user email & valid password', async () => {
        await loginPage.emailField.fill('<EMAIL>');
        await loginPage.passwordField.fill(standard_user_data.Password);
        await loginPage.loginButton.click();
        await expect(page.getByRole('heading', { name: 'Unable to log in' })).toBeVisible();
        await expect(page.getByText('You could not be logged in with those credentials')).toBeVisible();
    });

    await test.step('Attempt to login with valid email and invalid password', async () => {
        await loginPage.emailField.fill(standard_user_data.Email);
        await loginPage.passwordField.fill('notarealP@ssword123');
        await loginPage.loginButton.click();
        await expect(page.getByRole('heading', { name: 'Unable to log in' })).toBeVisible();
        await expect(page.getByText('You could not be logged in with those credentials')).toBeVisible();
    });

});

test('Login with Standard user', { tag: '@parallel', annotation:{type:'coa', description:'Login via username & password'} }, async ({ page, loginPage }) => {

    const standard_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Navigate to the Neo login page', async () => {
        await loginPage.goto();
    });

    await test.step('Login with standard user', async () => {
        await loginPage.emailField.fill(standard_user_data.Email);
        await loginPage.passwordField.fill(standard_user_data.Password);
        await loginPage.loginButton.click();
    });

    await test.step('Validate logged in (can see new booking screen)', async () => {
        await expect(page).toHaveURL('/neo/search');
        await expect(page.getByRole('img', { name: 'Matrix Booking logo' })).toBeVisible();
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
    });

});

test('Login with Admin user', { tag: '@parallel', annotation:{type:'coa', description:'Login via username & password'} }, async ({ page, loginPage }) => {

    const admin_user_data = require('../test-data/neo/Users/<USER>');

    await test.step('Navigate to the Neo login page', async () => {
        await loginPage.goto();
    });

    await test.step('Login with admin user', async () => {
        await loginPage.emailField.fill(admin_user_data.Email);
        await loginPage.passwordField.fill(admin_user_data.Password);
        await loginPage.loginButton.click();
    });

    await test.step('Validate logged in (can see new booking screen)', async () => {
        await expect(page).toHaveURL('/neo/search');
        await expect(page.getByRole('img', { name: 'Matrix Booking logo' })).toBeVisible();
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
    });

});

test('Login with Type 2 SSO user', { tag: '@parallel', annotation:{type:'coa', description:'Login via type 2 SSO'} }, async ({ page, loginPage, baseURL }) => {

    await test.step('Navigate to the Neo login page', async () => {
        await loginPage.goto();
    });

    await test.step('Start login with a type 2 SSO user (e.g. a 365 user like AlexW)', async () => {

        if (baseURL.toLowerCase().includes("int")) {

            await loginPage.emailField.fill('<EMAIL>');
            await expect(page.getByRole('button', { name: 'Log in with Matrix Int O365' })).toBeVisible();
            await page.getByRole('button', { name: 'Log in with Matrix Int O365' }).click();
            
        } else if (baseURL.toLowerCase().includes("dev")) {

            await loginPage.emailField.fill('<EMAIL>');
            await expect(page.getByRole('button', { name: 'Log in with Matrix Booking Dev SSO' })).toBeVisible();
            await page.getByRole('button', { name: 'Log in with Matrix Booking Dev SSO' }).click();

        } else if (baseURL.toLowerCase().includes("beta")) {

            await loginPage.emailField.fill('<EMAIL>');
            await expect(page.getByRole('button', { name: 'Log in with Matrix Beta Dev SSO' })).toBeVisible();
            await page.getByRole('button', { name: 'Log in with Matrix Beta Dev SSO' }).click();
            await page.getByPlaceholder('Email').fill('<EMAIL>');
            await page.getByText('Next').click();

        };

    });

    await test.step('Navigate through the Microsoft login process', async () => {
        await page.locator('#i0118').click({ force: true });
        await page.locator('#i0118').fill('plokij8u!');
        await page.getByRole('button', { name: 'Sign in' }).click();
        await page.getByRole('button', { name: 'Yes' }).click();
    });

    await test.step('Validate logged in (can see new booking screen)', async () => {
        await expect(page).toHaveURL('/neo/search');
        await expect(page.getByRole('heading', { name: 'New booking' })).toBeVisible();
    });

});