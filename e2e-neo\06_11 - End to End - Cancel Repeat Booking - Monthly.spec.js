import { test, expect } from './fixtures/pages.fixture';
import dayjs from 'dayjs';

let projectViewport;

test.beforeEach(async ({ }, testInfo) => {
    //This is the test setup. It runs before every test in this file.
    projectViewport = testInfo.project.use.viewport;
});

test('End to End - Cancel repeat booking - Monthly', { tag: '@serial', annotation: { type: 'coa', description: 'End to end - Cancel repeat booking - Every month' } }, async ({ page, loginPage, searchResultsPage, newBookingModalPage, myBookingsPage }) => {
    const email_user_data = require('../test-data/neo/Users/<USER>');

    // Move common variables to test scope
    const timeNow = dayjs();
    const bookingFromTime = timeNow.add(2, 'hour');
    const bookingToTime = dayjs(bookingFromTime).add(30, 'minutes');
    const repeatUntilDate = dayjs(bookingFromTime).add(6, 'month');
    const bookingTitle = `Cancel Monthly Test - Made by ${email_user_data.Name} - ${timeNow.format('DD-MM-YYYY HH꞉mm')}`;

    await test.step('Login and create a monthly repeat booking to cancel', async () => {
        await loginPage.simpleLogin(email_user_data);

        await page.getByRole('button', { name: 'Search' }).click();
        await searchResultsPage.bookResource('Basement Room 2');
        await newBookingModalPage.setStartTime(bookingFromTime);
        await newBookingModalPage.setEndTime(bookingToTime);
        await newBookingModalPage.setupRepeatBooking('Every month', repeatUntilDate, projectViewport.width);
        await newBookingModalPage.setTitle(bookingTitle);
        await newBookingModalPage.completeBooking();
        await newBookingModalPage.goToBookingsList(projectViewport.width);
    });

    // Function to get the same weekday each month
    const getMonthlyDates = (startDate, endDate) => {
        const dates = [];
        let currentDate = startDate;
        const targetWeekday = startDate.day(); // Get day of week (0-6)
        const weekInMonth = Math.ceil(startDate.date() / 7); // Get which week of the month

        // Add initial date
        dates.push(currentDate);

        // Get dates for following months
        while (currentDate.isBefore(endDate)) {
            // Move to first day of next month
            currentDate = currentDate.add(1, 'month').startOf('month');
            
            // Find the first occurrence of our weekday
            let targetDate = currentDate.day(targetWeekday);
            if (targetDate.isBefore(currentDate)) {
                targetDate = targetDate.add(1, 'week');
            }

            // Move to correct week of month
            targetDate = targetDate.add(weekInMonth - 1, 'week');

            // Only add if within our date range
            if (targetDate.isBefore(endDate) || targetDate.isSame(endDate, 'day')) {
                dates.push(targetDate);            }
        }

        return dates;
    };

    await test.step('Verify cancel dialog options for monthly repeat booking', async () => {
        await myBookingsPage.openCancelDialog(bookingFromTime, bookingTitle, projectViewport.width);

        await expect(myBookingsPage.cancelDialogTitle).toBeVisible();
        await expect(myBookingsPage.cancelDialogMessage).toHaveText('This action cannot be undone and will make the room available for others to book.');
        await expect(myBookingsPage.cancelSingleButton).toHaveText('Cancel just this booking');
        await expect(myBookingsPage.cancelAllButton).toHaveText('Cancel all remaining bookings in this series');
        await expect(myBookingsPage.cancelDialogCloseButton).toBeVisible();
        await expect(myBookingsPage.confirmCancelButton).toBeVisible();

        // Verify radio button states
        await expect(myBookingsPage.cancelSingleButton).toHaveClass(/cursor-not-allowed/);
        await expect(myBookingsPage.cancelSingleButton).toHaveClass(/bg-border-decoration/);
        await expect(myBookingsPage.cancelSingleButton).toBeDisabled();
        
        await expect(myBookingsPage.cancelAllButton).not.toHaveClass(/cursor-not-allowed/);
        await expect(myBookingsPage.cancelAllButton).not.toBeDisabled();

        await expect.soft(page).toHaveScreenshot('Neo-cancel-monthly-dialog.png');

        await myBookingsPage.cancelDialogCloseButton.click();
        if (projectViewport.width < 768) {
            await page.getByRole('button', { name: 'close' }).click();
        }
        await myBookingsPage.verifyBookingExists(bookingFromTime, bookingToTime, bookingTitle, projectViewport.width);
    });

    await test.step('Cancel single occurrence of monthly repeat booking', async () => {
        const monthlyDates = getMonthlyDates(bookingFromTime, repeatUntilDate);
        
        await myBookingsPage.cancelBooking(
            bookingFromTime,
            bookingToTime,
            bookingTitle,
            false,
            projectViewport.width
        );

        // Verify the next occurrence still exists using calculated date
        const nextMonthDate = monthlyDates[1];
        await myBookingsPage.verifyBookingExists(
            nextMonthDate.hour(bookingFromTime.hour()).minute(bookingFromTime.minute()),
            nextMonthDate.hour(bookingToTime.hour()).minute(bookingToTime.minute()),
            bookingTitle,
            projectViewport.width
        );

        await expect.soft(page).toHaveScreenshot('Neo-cancel-monthly-single.png');
    });

    await test.step('Cancel all remaining occurrences', async () => {
        const monthlyDates = getMonthlyDates(bookingFromTime, repeatUntilDate);
        const secondMonthDate = monthlyDates[1]; // Get second occurrence

        await myBookingsPage.cancelBooking(
            secondMonthDate.hour(bookingFromTime.hour()).minute(bookingFromTime.minute()),
            bookingToTime,
            bookingTitle,
            true,
            projectViewport.width
        );

        // Verify all remaining occurrences are cancelled using calculated dates
        for (let i = 1; i < monthlyDates.length; i++) {
            const date = monthlyDates[i];
            await myBookingsPage.verifyBookingDoesNotExist(
                date.hour(bookingFromTime.hour()).minute(bookingFromTime.minute()),
                bookingTitle,
                projectViewport.width
            );
        }

        await expect.soft(page).toHaveScreenshot('Neo-cancel-monthly-all.png');
    });
});