//import test and expect functionality
import { test, expect } from '@playwright/test';
import dayjs from 'dayjs';

//Set name of test for video
//change this
const testName = 'Logos and branding';

//Set video recording path
const recordingFilePath = `Videos/Webapp/${testName}/${testName} ${dayjs().format('DD-MM-YYYY HH꞉mm꞉ss')}.webm`
const recordingFilePathEmails = `Videos/Webapp/${testName}/${testName} - Emails ${dayjs().format('DD-MM-YYYY HH꞉mm꞉ss')}.webm`

test(testName, async ({ browser, baseURL }, testInfo) => {

  //Set up video recording for each tab - results in multiple videos
  const context = await browser.newContext({
    recordVideo: {
      dir: testInfo.outputPath('videos'),
      size: { width: 1500, height: 800 }
    },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36'
  });
  const page = await context.newPage();

  //Load in admin user data and org data
  const org = require(`../test-data/e2e/Org${testInfo.parallelIndex}_data.json`);
  const data = org.users.find(user => user.role === 'service_admin');

  await test.step('Login admin user', async () => {
    await page.goto('ui/#/login');
    await page.getByLabel('Email').fill(data.Email);
    await page.getByLabel('Password').fill(data.Password);
    await page.getByRole('button', { name: 'Log in' }).click();
    await expect(page.getByRole('heading', { name: `Welcome ${data.FirstName}` })).toBeVisible();
    await expect(page.locator('h1')).toContainText(`Welcome ${data.FirstName}`);
  });

  await test.step('Navigate to Logos and Branding', async () => {
    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await expect(page.getByText('Account Settings')).toBeVisible();
    await page.getByRole('link', { name: 'Logos and Branding' }).click();
    await expect(page.getByRole('img', { name: 'Logo' }).first()).toBeVisible();
  });

  await test.step('Upload a new Main Logo', async () => {
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.getByRole('button', { name: 'Upload' }).first().click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles('./test-data/Images/NewLogo.png');
    const responsePromise = await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo` && response.status() === 200);
  });


  await test.step('Compare new uploaded logo to golden file', async () => {

    await expect(page.getByRole('img', { name: 'Logo' }).first()).toHaveScreenshot('NewMainLogo.png');

    await expect(page.getByRole('img', { name: 'Home' })).toHaveScreenshot('NewMainLogoHOME.png');
    await expect(page.getByRole('img', { name: 'Powered by Matrix Booking' })).toBeVisible();
  });

  await test.step('Remove the new logo & recheck that we are back to the default logo', async () => {
    await page.getByRole('button', { name: 'Remove' }).click();
    // Wait for the API response to complete
    await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo?logoKind=MAIN` && response.status() === 204);
    await expect(page.getByRole('img', { name: 'Logo' }).first()).toHaveScreenshot('DefaultMainLogo.png');
    await expect(page.getByRole('img', { name: 'Home' })).toHaveScreenshot('DefaultMainLogoHOME.png');
    await expect(page.getByRole('img', { name: 'Powered by Matrix Booking' })).toBeHidden();
  });

  await test.step('Upload a new Reverse Logo', async () => {
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.getByRole('button', { name: 'Upload' }).nth(1).click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles('./test-data/Images/ReverseLogo.png');

    const responsePromise = await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo` && response.status() === 200);

  });

  await test.step('Compare new uploaded reverse logo to golden file', async () => {
    await expect(page.getByRole('img', { name: 'Logo' }).nth(1)).toHaveScreenshot('NewReverseLogo.png');
  });

  await test.step('Remove the new reverse logo & recheck that we are back to the default reverse logo', async () => {
    await page.getByRole('button', { name: 'Remove' }).click();
    // Wait for the API response to complete
    await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo?logoKind=REVERSE` && response.status() === 204);
    await expect(page.getByRole('img', { name: 'Logo' }).nth(1)).toHaveScreenshot('DefaultReverseLogo.png');
  });

  await test.step('Upload a new Email Logo', async () => {
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.getByRole('button', { name: 'Upload' }).nth(2).click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles('./test-data/Images/EmailLogo.png');

    const responsePromise = await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo` && response.status() === 200);
  });

  await test.step('Compare new uploaded email logo to golden file', async () => {
    await expect(page.getByRole('img', { name: 'Logo' }).nth(2)).toHaveScreenshot('NewEmailLogo.png');
  });

  await test.step('Change email header background colour (to a green in this case) and validate the changes on the admin page', async () => {
    await page.getByText('#ffffff').click();
    await page.getByTitle('#7ED321').click();
    await page.getByText('Email headerThis logo is used').click();
    await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo/emailHeaderColours` && response.status() === 200);
    await expect(page.locator('#main-content')).toContainText('#7ed321');
    const EmailHeaderBackground = page.locator('.tw-flex > div:nth-child(3)');
    const color = await EmailHeaderBackground.evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color") });
    expect(color).toBe("rgb(126, 211, 33)");
  });

  await test.step('Logout the admin user', async () => {
    await page.getByRole('link', { name: 'Home' }).click();
    await page.getByRole('link', { name: 'Switch' }).click();
    await page.getByRole('link', { name: 'Logout' }).click();
  });

  //INSERT STEPS HERE TO GENERATE AND CHECK EMAIL FOR THE LOGO CHANGES ABOVE
  //Load in email user details
  const email_user = org.users.find(user => user.role === 'email');
  //Define a new page ready for checking the outlook webmail
  const page1 = await context.newPage();

  await test.step('Open up new tab to and login to outlook emails', async () => {
    // Use the provided URL to access Outlook
    await page1.goto('https://go.microsoft.com/fwlink/p/?LinkID=2125442&deeplink=owa%2F%3Fstate%3D1%26redirectTo%3DaHR0cHM6Ly9vdXRsb29rLmxpdmUuY29tL21haWwv');

    // Email entry - use multiple selector strategies
    await expect(page1.getByRole('heading', { name: 'Sign in' })).toBeVisible();
    await page1.getByRole('textbox').filter({ type: 'email' }).fill(email_user.EmailOutlook);
    await page1.getByRole('button').filter({ hasText: /(Sign in|Next)/ }).last().click();

    // Password entry - wait for password field to be visible
    await expect(page1.getByRole('heading').filter({ hasText: /(Enter password|Enter your password)/ })).toBeVisible();

    // Target the password field using a more specific selector
    await page1.locator('[data-testid="passwordEntry"] input[type="password"], #i0118').fill(email_user.OutlookPassword);

    // Click Sign in button
    await page1.getByRole('button').filter({ hasText: /(Sign in|Next)/ }).last().click();

    // Handle "Stay signed in" prompt if it appears
    try {
      await page1.getByRole('button', { name: 'Yes' }).click({ timeout: 5000 });
    } catch (e) {
      console.log('No "Stay signed in" prompt appeared');
    }

  });

  await test.step('Login to the email user', async () => {
    await page.getByLabel('Email').fill(email_user.Email);
    await page.getByLabel('Password').fill(email_user.Password);
    await page.getByRole('button', { name: 'Log in' }).click();
    await expect(page.getByRole('heading', { name: `Welcome ${email_user.FirstName}` })).toBeVisible();
    await expect(page.locator('h1')).toContainText(`Welcome ${email_user.FirstName}`);
  });

  //Define variables for a booking
  const roomstring = 'Basement Room 1 Basement,'
  const BookingFromTime = dayjs().add(2, 'hour');
  const BookingToTime = BookingFromTime.add(1, 'hour');
  const BookingTitle = 'Here is a test booking 1 - ' + BookingFromTime.format('DD-MM-YYYY HH꞉mm꞉ss');

  await test.step('Create a single booking as the email user to generate an email', async () => {
    await page.getByRole('button', { name: 'Meeting Rooms' }).click();
    await page.getByRole('link', { name: 'Now' }).click();
    await page.locator('mb-availability-card').filter({ hasText: roomstring }).locator('button').click();
    await page.getByRole('textbox', { name: 'Date' }).fill(BookingFromTime.format('DD/MM/YYYY'));
    await page.getByRole('textbox', { name: 'Start' }).fill(BookingFromTime.format('H:mm'));
    await page.getByLabel('—').fill(BookingToTime.format('H:mm'));
    await page.getByPlaceholder('Title (optional)').fill(BookingTitle);
    await page.getByRole('button', { name: 'Book Basement Room' }).click();
    await expect(page.getByRole('heading')).toContainText('Successfully booked!');
    await page.getByText('Close').click();
  });

  await test.step('Open the booking email and validate the logo plus header backgrounds are now changed', async () => {
    await page1.getByLabel(`Has attachments Matrix Booking Basement Room 1 booked from ${BookingFromTime.format('h:mm A, dddd D')}`).getByText('Matrix Booking').click();
    await expect(page1.getByLabel('Reading Pane').getByRole('heading')).toContainText(`Basement Room 1 booked from ${BookingFromTime.format('h:mm A, dddd D MMMM YYYY')}`);
    await expect(page1.getByRole('row', { name: 'Booking Confirmed', exact: true }).getByRole('img')).toBeVisible();
    await expect(page1.getByRole('link', { name: 'Matrix' })).toHaveScreenshot('NewEmailLogoInOutlook.png');
    const EmailHeaderBackground = page1.locator('#x_mainMiddleTable > table > tbody > tr > td > table > tbody > tr > td').first();
    const color = await EmailHeaderBackground.evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color") });
    expect(color).toBe("rgb(126, 211, 33)");
  });

  await test.step('Cancel the booking', async () => {
    await page.getByRole('link', { name: 'My Bookings' }).click();
    await page.getByRole('button', { name: 'Cancel Booking ctrl.' }).click();
    await page.getByRole('button', { name: 'Cancel Booking' }).click();
  });

  await test.step('Clean up - Clear the outlook mailbox', async () => {

    //PLEASE NOTE: steps below are experimenting with using keyboard shortcuts to clear emails as spies in outlook for "select all" were unreliable
    //Implicit waits are used as the UI does not like the speed at which playwright sends the keyboard inputs
    await page1.goto("https://outlook.live.com/mail/0/")
    await expect(page1.getByLabel('Message list', { exact: true }).getByText('Inbox')).toBeVisible();
    await page1.locator('#MailList [role="option"]').first().click()
    const container = page1.locator('#ItemReadingPaneContainer');
    await container.waitFor(); // Wait for the element itself

    await page1.keyboard.press('Control+KeyA');
    await page1.waitForTimeout(500);
    await page1.keyboard.press('Delete');
    await page1.waitForTimeout(500);
    await page1.keyboard.press('Enter');


    /*
      await page1.locator('[aria-label="Select"]').click();
      await page1.locator('[aria-label="Select all messages"]').click();
      await page1.locator('[aria-label="Empty folder"]').first().click();
      await page1.getByRole('button', { name: 'OK' ,  exact: true  }).click();

    */

  });

  await test.step('Logout the email user', async () => {
    await page.getByRole('link', { name: 'Home' }).click();
    await page.getByRole('link', { name: 'Logout' }).click();
  });

  await test.step('Login the admin user again', async () => {
    await page.getByLabel('Email').fill(data.Email);
    await page.getByLabel('Password').fill(data.Password);
    await page.getByRole('button', { name: 'Log in' }).click();
    await expect(page.getByRole('heading', { name: `Welcome ${data.FirstName}` })).toBeVisible();
    await expect(page.locator('h1')).toContainText(`Welcome ${data.FirstName}`);
  });

  await test.step('Navigate to Logos and Branding', async () => {
    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await expect(page.getByText('Account Settings')).toBeVisible();
    await page.getByRole('link', { name: 'Logos and Branding' }).click();
    await expect(page.getByRole('img', { name: 'Logo' }).first()).toBeVisible();
  });

  await test.step('Remove the new email logo & recheck that we are back to the default email logo', async () => {
    await page.getByRole('button', { name: 'Remove' }).click();
    await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo?logoKind=EMAIL` && response.status() === 204);
    await expect(page.getByRole('img', { name: 'Logo' }).nth(2)).toHaveScreenshot('DefaultEmailLogo.png');
  });

  await test.step('Set email header background colour back to white and validate the changes on the admin page', async () => {
    await page.getByText('#7ed321').click();
    await page.getByTitle('#FFFFFF').click();
    await page.getByText('Email headerThis logo is used').click();
    await page.waitForResponse(response => response.url() == `${baseURL}api/v1/org/${org.orgID}/logo/emailHeaderColours` && response.status() === 200);
    await expect(page.locator('#main-content')).toContainText('#ffffff');
    const EmailHeaderBackground = page.locator('.tw-flex > div:nth-child(3)');
    const color = await EmailHeaderBackground.evaluate((e) => { return window.getComputedStyle(e).getPropertyValue("background-color") });
    expect(color).toBe("rgb(255, 255, 255)");
  });

  // Close the context and save recordings
  await Promise.all([
    page.video().saveAs(recordingFilePath),
    page1.video().saveAs(recordingFilePathEmails),
    context.close()
  ]);
  testInfo.attachments.push({
    name: 'video',
    path: recordingFilePath,
    contentType: 'video/webm'
  },
    {
      name: 'video',
      path: recordingFilePathEmails,
      contentType: 'video/webm'
    }
  );

});