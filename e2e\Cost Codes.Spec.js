//import test and expect functionality
import { test, expect } from '@playwright/test';
import dayjs from 'dayjs';

//Set name of test for video
//change this
const testName = 'Cost Codes';

//Set video recording path
const recordingFilePath = `Videos/Webapp/${testName}/${testName} ${dayjs().format('DD-MM-YYYY HH꞉mm꞉ss')}.webm`

test(testName, async ({ browser }, testInfo) => {
  //Set up video recording for every test
  // Launch a browser context with video recording enabled
  //set testInfo to record video
  // Create context with custom userAgent
  const context = await browser.newContext({
    recordVideo: {
      dir: testInfo.outputPath('videos'),
      size: { width: 1500, height: 800 },
    },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.33 Safari/537.36'
  });

  // Create a new page
  const page = await context.newPage();
  const org = require(`../test-data/e2e/Org${testInfo.parallelIndex}_data.json`);
  const data = org.users.find(user => user.role === 'service_admin');

  await test.step('Login service admin user from service admin json', async () => {
    await page.goto('ui/#/login');
    await page.getByLabel('Email').fill(data.Email);
    await page.getByLabel('Password').fill(data.Password);
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.getByRole('link', { name: 'Switch' }).click();
    await page.getByPlaceholder('Start typing an organisation').fill(org.orgName);
    await page.getByRole('link', { name: org.orgName }).click();
    await expect(page.getByRole('banner')).toContainText(org.orgName);
    await expect(page.getByRole('heading', { name: 'Welcome serviceadmin' })).toBeVisible();
    await expect(page.locator('h1')).toContainText('Welcome serviceadmin');
  });

  await test.step('AND: ORG_ADMIN_CAN_MANAGE_COST_CODES is on for the org', async () => {
    //Navigate to admin area and account flags to check that FIND_SOMEONE flag is enabled. (On a new Org it is enabled by default)
    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await expect(page.getByText('Account Settings')).toBeVisible();
    await page.getByRole('link', { name: 'Account ' }).click();
    await expect(page.getByRole('heading', { name: 'Feature Switches (Config' })).toBeVisible();
    await page.getByRole('button', { name: 'Released' }).click();
    await expect(page.getByRole('cell', { name: 'ORG_ADMIN_CAN_MANAGE_COST_CODES' })).toBeVisible();

    const manageCostCodesButton = await page.getByRole('row', { name: 'ORG_ADMIN_CAN_MANAGE_COST_CODES' }).locator('button');

    // Check if the button has the 'fa-toggle-on' class, indicating it's active, and click if not
    if (!await manageCostCodesButton.locator('.fa-toggle-on').isVisible()) {
      await manageCostCodesButton.click();
    }

    await expect(page.getByRole('row', { name: 'ORG_ADMIN_CAN_MANAGE_COST_CODES' }).locator('button')).toBeChecked;
    await page.waitForLoadState('domcontentloaded')
    await page.waitForLoadState('networkidle');
    await page.goto('ui');

    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await expect(page.getByText('Account Settings')).toBeVisible();
    await page.getByRole('link', { name: 'Account ' }).click();
    await expect(page.getByRole('heading', { name: 'Feature Switches (Config' })).toBeVisible();
    await page.getByRole('button', { name: 'Released' }).click();
    await expect(page.getByRole('cell', { name: 'COST_CODE_VALIDATE	' })).toBeVisible();
    const costCodeValidateButton = await page.getByRole('row', { name: 'COST_CODE_VALIDATE' }).locator('button');

    // Check if the button has the 'fa-toggle-on' class, indicating it's active, and click if not
    if (!await costCodeValidateButton.locator('.fa-toggle-on').isVisible()) {
      await costCodeValidateButton.click();
    }
    await expect(page.getByRole('row', { name: 'COST_CODE_VALIDATE' }).locator('button')).toBeChecked;
    await page.waitForLoadState('domcontentloaded')
    await page.waitForLoadState('networkidle');
    await page.goto('ui');

    await page.getByRole('link', { name: 'Switch' }).click();
    await page.getByRole('link', { name: 'Logout' }).click();
    await expect(page.getByLabel('Email')).toBeVisible();
  });


  const admin_user_data = org.users.find(user => user.role === 'admin');

  await test.step('Login as an admin user', async () => {
    await page.goto('ui/#/login');
    await expect(page.getByRole('img', { name: 'Matrix Booking' })).toBeVisible();
    await expect(page.getByLabel('Email')).toBeVisible();
    await page.getByLabel('Email').fill(admin_user_data.Email);
    await page.getByLabel('Password').fill(admin_user_data.Password);
    await page.getByRole('button', { name: 'Log in' }).click();
    await expect(page.getByRole('banner')).toContainText(org.orgName);
    await expect(page.locator('h1')).toContainText('Welcome admin');
  });

  await test.step('Navigate to the Cost Codes page', async () => {
    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    //THEN: Cost Codes is present in the admin list
    await expect(page.getByText('Account Settings Administrators Booking Categories Facilities Booking Options').getByRole('link', { name: 'Cost Codes', exact: true })).toBeVisible();
    //AND: Clicking the Cost Codes option takes me to the Cost Codes Admin screen
    await page.getByRole('link', { name: 'Cost Codes', exact: true }).click();
    await expect(page.getByRole('heading', { name: 'Cost Code' })).toBeVisible();
  });

  await test.step('Entering more than 255 characters causes the counter to go red indicating the maximum character length has been breached', async () => {
    await page.getByPlaceholder('Type the name of a new Cost').fill('**********');
    await expect(page.getByRole('table')).toContainText('10/255');
    await page.getByPlaceholder('Type the name of a new Cost').fill('********************12453434D3456Q7890**********************************************************************************************************************************************************************************************12345FGDFGDFGDFGDFGDFGDDFGF');
    await expect(page.getByRole('table')).toContainText('255/255');

    const costCodeInputElement = await page.getByPlaceholder('Type the name of a new Cost');
    const borderColor = await costCodeInputElement.evaluate(el => getComputedStyle(el).borderBottomColor);
    expect(borderColor).not.toBe('rgb(235, 20, 28)');

    const descriptionElement = await page.getByText('255/');
    const descriptionElementBorderColor = await descriptionElement.evaluate(el => getComputedStyle(el).color);
    expect(descriptionElementBorderColor).toBe('rgb(117, 117, 117)');

    await page.getByPlaceholder('Type the name of a new Cost').fill('1********************12453434D3456Q7890**********************************************************************************************************************************************************************************************12345FGDFGDFGDFGDFGDFGDDFGF');
    await expect(page.getByRole('table')).toContainText('256/255');

    await page.waitForTimeout(1000);
    const costCodeInputElementRed = await page.getByPlaceholder('Type the name of a new Cost');
    const borderBottomColor = await costCodeInputElementRed.evaluate(el => getComputedStyle(el).borderBottomColor);
    expect(borderBottomColor).toBe('rgb(235, 20, 28)');

    const descriptionElementRed = await page.getByText('/255');
    const color = await descriptionElementRed.evaluate(el => getComputedStyle(el).color);
    expect(color).toBe('rgb(235, 20, 28)');

  });
  await test.step('Entering more than 50 characters causes the counter to go red indicating the maximum character length has been breached', async () => {
    await page.getByPlaceholder('Type the Cost Code Description').fill('**********');
    await expect(page.getByRole('table')).toContainText('10/50');
    await page.getByPlaceholder('Type the Cost Code Description').fill('12312312312312312312312312312312312312312311231111');
    await expect(page.getByRole('table')).toContainText('50/50');

    const costCodeInputElement = await page.getByPlaceholder('Type the Cost Code Description')
    const costCodeInputColor = await costCodeInputElement.evaluate(el => getComputedStyle(el).borderBottomColor);
    expect(costCodeInputColor).not.toBe('rgb(235, 20, 28)');

    const descriptionTextElement = await page.getByText('/50');
    const descriptionTextColor = await descriptionTextElement.evaluate(el => getComputedStyle(el).color);
    expect(descriptionTextColor).toBe('rgb(117, 117, 117)');

    await page.getByPlaceholder('Type the Cost Code Description').fill('123123123123123123123123123123123123123123112311111');
    await expect(page.getByRole('table')).toContainText('51/50');
    await page.waitForTimeout(1000);

    const costDescriptionInputElement = await page.getByPlaceholder('Type the Cost Code Description');
    const costDescriptionInputColor = await costDescriptionInputElement.evaluate(el => getComputedStyle(el).borderBottomColor);
    expect(costDescriptionInputColor).toBe('rgb(235, 20, 28)');

    const descriptionCountTextElement = await page.getByText('/50');
    const descriptionCountTextColor = await descriptionCountTextElement.evaluate(el => getComputedStyle(el).color);
    expect(descriptionCountTextColor).toBe('rgb(235, 20, 28)');

  });
  /*
    GIVEN: I have entered > 255 characters in the ‘Cost Code’ field
    AND: < 51 characters in the ‘Description’ field
  */
  await test.step('Fail to add cost code, cost code', async () => {
    await page.getByPlaceholder('Type the name of a new Cost').fill('1********************12453434D3456Q7890**********************************************************************************************************************************************************************************************12345FGDFGDFGDFGDFGDFGDDFGF');
    await page.getByPlaceholder('Type the Cost Code Description').fill('**********');
    expect(await page.getByRole('cell', { name: 'Add' }).locator('button').evaluate(button => button.disabled)).toBe(true);
  });

  /*GIVEN: I have entered > 50 characters in the ‘Description’ field
  AND: < 256 characters in the ‘Cost Code’ field
  WHEN: I select the ‘Add’ button
  THEN: The new cost code is not added to the list
  */
  await test.step('Fail to add cost code description', async () => {
    await page.getByPlaceholder('Type the Cost Code Description').fill('123123123123123123123123123123123123123123112311111');
    await page.getByPlaceholder('Type the name of a new Cost').fill('**********');
    expect(await page.getByRole('cell', { name: 'Add' }).locator('button').evaluate(button => button.disabled)).toBe(true);
  });

  await test.step('THEN: The cost code is added to the list', async () => {
    await page.getByPlaceholder('Type the Cost Code Description').fill('Cost Code Description');
    await page.getByPlaceholder('Type the name of a new Cost').fill('Cost Code');
    await page.getByRole('button', { name: 'Add' }).click();
    await expect(page.getByRole('cell', { name: 'COST CODE', exact: true })).toBeVisible();
    await page.getByPlaceholder('Type the Cost Code Description').fill('a Cost Code Description');
    await page.getByPlaceholder('Type the name of a new Cost').fill('a Cost Code');
    await page.getByRole('button', { name: 'Add' }).click();
    await expect(page.getByRole('cell', { name: 'a Cost Code Description', exact: true })).toBeVisible();
    await page.getByPlaceholder('Type the Cost Code Description').fill('1 Cost Code Description');
    await page.getByPlaceholder('Type the name of a new Cost').fill('1 Cost Code');
    await page.getByRole('button', { name: 'Add' }).click();
    await expect(page.getByRole('cell', { name: '1 Cost Code Description', exact: true })).toBeVisible();
  });


  await test.step('AND: The list is presented in alpha numeric order sorted on the Cost Code', async () => {
    await page.reload();
    // Second row
    expect(await page.locator('table tbody tr:nth-child(2) td:nth-child(2) div').textContent()).toContain('1 COST CODE');
    expect(await page.locator('table tbody tr:nth-child(2) td:nth-child(3) div').textContent()).toContain('1 Cost Code Description');

    // Third row
    expect(await page.locator('table tbody tr:nth-child(3) td:nth-child(2) div').textContent()).toContain('A COST CODE');
    expect(await page.locator('table tbody tr:nth-child(3) td:nth-child(3) div').textContent()).toContain('a Cost Code Description');

    // Fourth row
    expect(await page.locator('table tbody tr:nth-child(4) td:nth-child(2) div').textContent()).toContain('COST CODE');
    expect(await page.locator('table tbody tr:nth-child(4) td:nth-child(3) div').textContent()).toContain('Cost Code Description');
  });

  await test.step('THEN: I am presented with the error This cost code already exists.', async () => {
    await page.getByPlaceholder('Type the Cost Code Description').fill('Cost Code Description');
    await page.getByPlaceholder('Type the name of a new Cost').fill('Cost Code');
    await page.getByRole('button', { name: 'Add' }).click();
    await expect(page.getByRole('alert')).toBeVisible();
    await expect(page.getByRole('paragraph')).toContainText('This cost code already exists.');
    await expect(page.getByPlaceholder('Type the name of a new Cost')).toHaveValue('COST CODE');
    await expect(page.getByPlaceholder('Type the Cost Code Description')).toHaveValue('Cost Code Description');
  });

  //AND: The new cost code can be added to a booking in both List and free text org settings
  await test.step('require cost code', async () => {
    await page.getByRole('link', { name: 'Locations and Resources' }).click();
    await page.getByRole('button', { name: 'Edit hierarchy' }).click();
    await page.locator('.modal-backdrop').waitFor({ state: 'hidden' });
    await page.waitForTimeout(500);
    await expect(page.getByRole('button', { name: 'Building Test Building 1' })).toBeVisible();
    await page.getByRole('button', { name: 'Meeting Room Basement Room 2' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Show panel Settings' }).click();
    await page.locator('mb-location-booking-settings').filter({ hasText: 'Cost codes Override global' }).getByLabel('Override global settings').check();
    await expect(page.getByLabel('Require a cost code to be')).toBeChecked();
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('button', { name: 'Building Test Building 1' })).toBeVisible()

    await page.getByRole('link', { name: 'New Booking' }).click();
  });

  await test.step('make booking require cost code', async () => {
    await page.getByRole('button', { name: 'Meeting Rooms' }).click();
    await page.getByRole('link', { name: 'Today' }).click();
    await page.getByRole('button', { name: 'Basement Room 2 Basement,' }).click();
    await page.getByRole('button', { name: 'Basement Room 2', exact: true }).click();
    await page.getByRole('textbox', { name: 'Start' }).fill(dayjs().add(1, 'hour').format('HH:mm'));
    await page.getByLabel('—').fill(dayjs().add(2, 'hour').format('HH:mm'));

    await page.getByPlaceholder('Cost Code').fill('COST');
    await page.getByRole('button', { name: 'Book Basement Room' }).click();
    await expect(page.getByText('Unable to book room')).toBeVisible();
    await expect(page.getByText('Please specify a valid cost')).toBeVisible();
    await expect(page.getByText('Unable to book room Please')).toBeVisible();

    await page.getByPlaceholder('Cost Code').fill('COST CODE');
    await page.getByRole('button', { name: 'Book Basement Room' }).click();

    await expect(page.getByRole('img', { name: 'Cost Code' })).toBeVisible();
    await expect(page.getByText('COST CODE')).toBeVisible();
    await expect(page.locator('fieldset').filter({ hasText: 'COST CODE' })).toBeVisible();

    await page.getByRole('button', { name: 'My Bookings' }).click();
    await page.getByLabel('Search results', { exact: true }).getByText("Basement Room 2").locator('..').locator('..').locator('..').getByRole('button', { name: 'Edit Booking ctrl.' }).click();
    await page.getByPlaceholder('Cost Code').fill('A COST CODE');
    await page.getByRole('button', { name: 'Save' }).click();
    await page.getByLabel('Search results', { exact: true }).getByText("Basement Room 2").locator('..').locator('..').locator('..').getByRole('button', { name: 'Edit Booking ctrl.' }).click();
    await expect(page.getByPlaceholder('Cost Code')).toHaveValue('A COST CODE');
    await page.getByRole('button', { name: 'Cancel Booking' }).click();
    await page.getByRole('button', { name: 'Cancel Booking' }).click();
  });

  await test.step('enable cost code LIST', async () => {
    await page.getByRole('link', { name: 'Logout' }).click();
    await expect(page.getByRole('img', { name: 'Matrix Booking' })).toBeVisible();
    await expect(page.getByLabel('Email')).toBeVisible();
    await page.getByLabel('Email').fill(data.Email);
    await page.getByLabel('Password').fill(data.Password);
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.getByRole('link', { name: 'Switch' }).click();
    await page.getByPlaceholder('Start typing an organisation').fill(org.orgName);
    await page.getByRole('link', { name: org.orgName }).click();
    await expect(page.getByRole('banner')).toContainText(org.orgName);
    await expect(page.getByRole('heading', { name: 'Welcome serviceadmin' })).toBeVisible();
    await expect(page.locator('h1')).toContainText('Welcome serviceadmin');
    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await expect(page.getByText('Account Settings')).toBeVisible();
    await page.getByRole('link', { name: 'Account ' }).click();
    await page.getByRole('button', { name: 'Released' }).click();
    await expect(page.getByRole('cell', { name: 'COST_CODE_LIST' })).toBeVisible();

    const costCodeListButton = await page.getByRole('row', { name: 'COST_CODE_LIST' }).locator('button');
    // Check if the button has the 'fa-toggle-on' class, indicating it's active, and click if not
    if (!await costCodeListButton.locator('.fa-toggle-on').isVisible()) {
      await costCodeListButton.click();
    }
    await expect(page.getByRole('row', { name: 'COST_CODE_LIST' }).locator('button')).toBeChecked;

    await page.waitForLoadState('domcontentloaded')
    await page.waitForLoadState('networkidle');
    await page.goto('ui');
  });

  await test.step('check cost code list', async () => {
    await page.getByRole('button', { name: 'Meeting Rooms' }).click();
    await page.getByRole('link', { name: 'Today' }).click();
    await page.getByRole('button', { name: 'Basement Room 2 Basement,' }).click();
    await page.getByRole('button', { name: 'Basement Room 2', exact: true }).click();
    await page.getByRole('textbox', { name: 'Start' }).fill(dayjs().add(1, 'hour').format('HH:mm'));
    await page.getByLabel('—').fill(dayjs().add(2, 'hour').format('HH:mm'));
    await page.getByRole('combobox').selectOption("Cost Code Description");
    await expect(await page.locator('select[ng-model="pickedCostCode"] option:checked').textContent()).toBe('Cost Code Description');
    await page.getByRole('combobox').selectOption("1 Cost Code Description");
    await expect(await page.locator('select[ng-model="pickedCostCode"] option:checked').textContent()).toBe('1 Cost Code Description');
    await page.getByRole('combobox').selectOption("a Cost Code Description");
    await expect(await page.locator('select[ng-model="pickedCostCode"] option:checked').textContent()).toBe('a Cost Code Description');

    await page.getByRole('button', { name: 'Book Basement Room' }).click();
    await expect(page.getByText('A COST CODE')).toBeVisible();
    await expect(page.locator('form[name="forms\\.bookingForm"]')).toContainText('COST CODE');

    await page.getByRole('button', { name: 'My Bookings' }).click();
    await page.getByLabel('Search results', { exact: true }).getByText("Basement Room 2").locator('..').locator('..').locator('..').getByRole('button', { name: 'Edit Booking ctrl.' }).click();
    await page.getByRole('combobox').selectOption("1 Cost Code Description");
    await page.getByRole('button', { name: 'Save' }).click();
    await page.waitForTimeout(1000);
    await page.getByLabel('Search results', { exact: true }).getByText("Basement Room 2").locator('..').locator('..').locator('..').getByRole('button', { name: 'Edit Booking ctrl.' }).click();
    await expect(await page.locator('select[ng-model="pickedCostCode"] option:checked').textContent()).toBe('1 Cost Code Description');
    await page.getByRole('button', { name: 'Cancel Booking' }).click();
    await page.getByRole('button', { name: 'Cancel Booking' }).click();
  });

  await test.step('Export', async () => {
    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await page.getByRole('link', { name: 'Cost Codes', exact: true }).click();
    const downloadPromise = page.waitForEvent('download');
    await page.getByRole('button', { name: 'Export to CSV' }).click();
    const download = await downloadPromise;

    // Get the file path where the download is saved
    const downloadPath = `Videos/Webapp/${testName}/downloaded_file.csv`; // Specify the path where you want to save the file
    await download.saveAs(downloadPath);

    // Use Node.js 'fs' module to read the CSV file content
    const fs = require('fs');
    const csvContent = fs.readFileSync(downloadPath, 'utf-8');

    // Perform assertions on the CSV content
    expect(csvContent).toContain('1 COST CODE,1 Cost Code Description');
    expect(csvContent).toContain('A COST CODE,a Cost Code Description');
    expect(csvContent).toContain('COST CODE,Cost Code Description');
  });


  await test.step('Delete Cost codes', async () => {
    await page.getByRole('row', { name: '1 COST CODE 1 Cost Code' }).getByRole('checkbox').check();
    await expect(page.getByRole('button', { name: 'Delete (1)' })).toBeVisible();
    await page.getByRole('button', { name: 'Delete (1)' }).click();
    await expect(page.getByRole('heading', { name: 'Confirmation' })).toBeVisible();
    await expect(page.getByText('Are you sure that you want to')).toBeVisible();
    await expect(page.getByRole('article')).toContainText('Are you sure that you want to delete 1 cost code?');
    await expect(page.getByRole('button', { name: 'Delete' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Cancel' })).toBeVisible();
    await page.getByRole('button', { name: 'Cancel' }).click();
    await expect(page.getByRole('table')).toContainText('1 COST CODE');
    await expect(page.getByRole('table')).toContainText('1 Cost Code Description');

    await expect(page.getByRole('button', { name: 'Delete (1)' })).toBeVisible();
    await page.getByRole('button', { name: 'Delete (1)' }).click();
    await expect(page.getByRole('heading', { name: 'Confirmation' })).toBeVisible();
    await expect(page.getByText('Are you sure that you want to')).toBeVisible();
    await expect(page.getByRole('article')).toContainText('Are you sure that you want to delete 1 cost code?');
    await expect(page.getByRole('button', { name: 'Delete' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Cancel' })).toBeVisible();
    await page.getByRole('button', { name: 'Delete' }).click();

    await expect(page.getByText('1 COST CODE')).toHaveCount(0);
    await expect(page.getByText('1 Cost Code Description')).toHaveCount(0);


    await page.getByPlaceholder('Type the name of a new Cost').fill('1 Cost Code');
    await page.getByPlaceholder('Type the Cost Code Description').fill("1 Cost Code Description")
    await page.getByRole('button', { name: 'Add' }).click();

    await page.getByRole('row', { name: 'A COST CODE a Cost Code' }).getByRole('checkbox').check();
    await page.getByRole('row', { name: 'COST CODE Cost Code' }).getByRole('checkbox').check();
    await expect(page.getByRole('button', { name: 'Delete (2)' })).toBeVisible();
    await page.getByRole('button', { name: 'Delete (2)' }).click();
    await page.getByRole('button', { name: 'Delete' }).click();


    await expect(page.getByText('A COST CODE')).toHaveCount(0);
    await expect(page.getByText('A Cost Code Description')).toHaveCount(0);

    await page.getByPlaceholder('Type the name of a new Cost').fill('A Cost Code');
    await page.getByPlaceholder('Type the Cost Code Description').fill("A Cost Code Description")
    await page.getByRole('button', { name: 'Add' }).click();

    await page.getByRole('row', { name: '/255 0/50 Add' }).getByRole('checkbox').check();
    await expect(page.getByRole('button', { name: 'Delete' })).toBeVisible();
    await page.getByRole('button', { name: 'Delete' }).click();
    await page.getByRole('button', { name: 'Delete' }).click();
    await expect(page.getByText('No Cost Codes have been added')).toBeVisible();
    await expect(page.getByRole('alert')).toBeVisible();
  });

  await test.step('Clean up', async () => {
    await page.getByRole('link', { name: 'Account ' }).click();
    await expect(page.getByRole('heading', { name: 'Feature Switches (Config' })).toBeVisible();

    await page.getByRole('row', { name: 'COST_CODE_LIST' }).locator('button').click();
    await expect(page.getByRole('row', { name: 'COST_CODE_LIST' }).locator('button')).not.toBeChecked;
    await page.waitForLoadState('domcontentloaded')
    await page.waitForLoadState('networkidle');
    await page.goto('ui');

    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await expect(page.getByText('Account Settings')).toBeVisible();
    await page.getByRole('link', { name: 'Account ' }).click();
    await expect(page.getByRole('heading', { name: 'Feature Switches (Config' })).toBeVisible();

    await page.getByRole('row', { name: 'COST_CODE_VALIDATE' }).locator('button').click();
    await expect(page.getByRole('row', { name: 'COST_CODE_VALIDATE' }).locator('button')).not.toBeChecked;
    await page.waitForLoadState('domcontentloaded')
    await page.waitForLoadState('networkidle');
    await page.goto('ui');

    /*
    GIVEN my booking requires a manually entered cost code
    WHEN I am typing in the cost code
    AND if i exceed 50 char's
    THEN I cannot save my booking
    AND a warning is shown "Unable to Book (resource) The maximum length for a Cost Code is 50 characters"
    */
    await test.step('Verify cost code length validation with COST_CODE_VALIDATE off', async () => {
      // Navigate to make a booking
      await page.getByRole('button', { name: 'Meeting Rooms' }).click();
      await page.getByRole('link', { name: 'Today' }).click();
      await page.getByRole('button', { name: 'Basement Room 2 Basement,' }).click();
      await page.getByRole('button', { name: 'Basement Room 2', exact: true }).click();

      // Set booking time
      await page.getByRole('textbox', { name: 'Start' }).fill(dayjs().add(1, 'hour').format('HH:mm'));
      await page.getByLabel('—').fill(dayjs().add(2, 'hour').format('HH:mm'));

      // Enter a cost code that exceeds 50 characters
      const longCostCode = '**************************************************1'; // 51 characters
      await page.getByPlaceholder('Cost Code').fill(longCostCode);

      // Try to book and verify error message
      await page.getByRole('button', { name: 'Book Basement Room' }).click();
      await expect(page.getByText('Unable to Book')).toBeVisible();
      await expect(page.getByText('The maximum length of a cost code is 50 characters')).toBeVisible();

      // Verify we cannot save the booking with the long cost code
      await expect(page.getByRole('heading', { name: 'Successfully booked!' })).not.toBeVisible();

      // Try with a valid cost code (50 characters exactly)
      const validCostCode = '**************************************************'; // 50 characters
      await page.getByPlaceholder('Cost Code').fill(validCostCode);
      await page.getByRole('button', { name: 'Book Basement Room' }).click();

      // Verify booking is successful
      await expect(page.getByRole('heading', { name: 'Successfully booked!' })).toBeVisible();

      // Cancel the booking to clean up
      await page.getByRole('button', { name: 'My Bookings' }).click();
      await page.getByLabel('Search results', { exact: true }).getByText("Basement Room 2").locator('..').locator('..').locator('..').getByRole('button', { name: 'Edit Booking ctrl.' }).click();
      await page.getByRole('button', { name: 'Cancel Booking' }).click();
      await page.getByRole('button', { name: 'Cancel Booking' }).click();
    });

    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await expect(page.getByText('Account Settings')).toBeVisible();
    await page.getByRole('link', { name: 'Account ' }).click();
    await expect(page.getByRole('heading', { name: 'Feature Switches (Config' })).toBeVisible();

    await page.getByRole('row', { name: 'ORG_ADMIN_CAN_MANAGE_COST_CODES' }).locator('button').click();
    await expect(page.getByRole('row', { name: 'ORG_ADMIN_CAN_MANAGE_COST_CODES' }).locator('button')).not.toBeChecked;

    await page.waitForLoadState('domcontentloaded')
    await page.waitForLoadState('networkidle');
    await page.goto('ui');

    await page.getByRole('link', { name: 'Admin', exact: true }).click();
    await page.getByRole('link', { name: 'Locations and Resources' }).click();
    await page.getByRole('button', { name: 'Edit hierarchy' }).click();
    await page.locator('.modal-backdrop').waitFor({ state: 'hidden' });
    await page.waitForTimeout(500);
    await expect(page.getByRole('button', { name: 'Building Test Building 1' })).toBeVisible();
    await page.getByRole('button', { name: 'Meeting Room Basement Room 2' }).getByRole('button').first().click();
    await page.getByRole('button', { name: 'Show panel Settings' }).click();
    await page.locator('mb-location-booking-settings').filter({ hasText: 'Cost codes Override global' }).getByLabel('Override global settings').uncheck();
    await page.getByRole('button', { name: 'Save' }).click();
    await page.getByRole('link', { name: 'New Booking' }).click();
    await expect(page.getByRole('heading', { name: 'Welcome serviceadmin' })).toBeVisible();

    await page.getByRole('link', { name: 'Switch' }).click();
    await page.getByRole('link', { name: 'Logout' }).click();
  });

  // Close the context to stop video recording
  await Promise.all([
    page.video().saveAs(recordingFilePath),
    page.close()
  ]);
  testInfo.attachments.push({
    name: 'video',
    path: recordingFilePath,
    contentType: 'video/webm'
  });

});